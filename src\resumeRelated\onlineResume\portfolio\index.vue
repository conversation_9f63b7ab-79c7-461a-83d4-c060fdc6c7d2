<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="作品集上传">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-card">
        <view class="flex-c border-b p-b-40rpx p-t-20rpx">
          <view class="mainText m-r-10rpx">作品URL</view>
          <wd-input
            class="flex-1"
            no-border
            v-model="url"
            border="none"
            custom-input-class="custom-class"
            placeholder="请输入作品集名称"
          ></wd-input>
        </view>
        <wd-upload
          reupload
          v-model:file-list="fileList"
          image-mode="aspectFit"
          :limit="1"
          :header="header"
          :action="baseUrl"
          @success="successFun"
          style="margin: auto"
          custom-class="custom-class-1"
          accept="image"
          :max-size="10 * 1024 * 1024"
        >
          <view class="m-t-40rpx img-upload">
            <view class="img-upload-icon"></view>
          </view>
          <view class="text-c p-t-20rpx p-b-20rpx dark-color text-28rpx">作品集上传</view>
        </wd-upload>
      </view>
      <view class="subText p-t-20rpx">备注：证书文字清晰、证书编号清晰</view>
    </view>

    <template #bottom>
      <view class="btn-fixed flex-c">
        <view class="btn_box" @click="addSubmit">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { encryption } from '@/service/crypto'
import { resumeFileAdd, resumeFileUpdate } from '@/interPost/resume'
import { baseUrlImgCommon, baseUrlPrever } from '@/interPost/img'
import { useMessage } from 'wot-design-uni'
const { getToken } = useUserInfo()
const message = useMessage()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
// 初始化的数据
const attachmentIdInit = ref('')
const urlInit = ref('')
const attachmentUrl = ref('')
// 图片上传
const baseUrl = baseUrlImgCommon
// 简历id
const baseInfoId = ref(null)
// id
const id = ref(null)
// 上传后的证书id
const attachmentId = ref('')
// 证书
const url = ref('')
// 新增和编辑
const isAdd = ref(null)
const fileList = ref([])
// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})
//
onLoad(async (options) => {
  await nextTick()
  console.log(options, 'options==')
  baseInfoId.value = options.id
  attachmentUrl.value = options.attachmentUrl
  id.value = options.sid
  isAdd.value = options.isAdd
  // 赋值
  url.value = options.url
  attachmentId.value = options.attachmentId
  // 判断初始化的数据
  urlInit.value = options.url
  attachmentIdInit.value = options.attachmentId

  if (attachmentId.value) {
    const imageUrl = baseUrlPrever + '/' + encryption(attachmentId.value)
    console.log(imageUrl, 'imageUrl========')
    fileList.value = [
      {
        url: attachmentUrl.value,
        name: 'tupian',
      },
    ]
  }
})

// 图片上传成功
const successFun = ({ fileList }) => {
  const res = JSON.parse(fileList[0].response)
  // const res = JSON.parse(decryption(resJson.data))
  // console.log(res, '图片上传')
  if (res.code === 0) {
    attachmentId.value = res.data[0].fileId
  }
}

// 提交
const addSubmit = async () => {
  if (isAdd.value === 'add') {
    const res: any = await resumeFileAdd({
      url: url.value,
      attachmentId: attachmentId.value,
      baseInfoId: baseInfoId.value,
      status: 1,
      sortNo: 1,
    })
    if (res.code === 0) {
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  } else {
    const res: any = await resumeFileUpdate({
      url: url.value,
      attachmentId: attachmentId.value,
      baseInfoId: baseInfoId.value,
      status: 1,
      sortNo: 1,
      id: id.value,
    })
    if (res.code === 0) {
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  }
}
// 返回
const back = () => {
  if (attachmentIdInit.value === attachmentId.value && url.value === urlInit.value) {
    uni.navigateBack()
  } else {
    message
      .confirm({
        title: '提示',
        msg: '您有内容未提交保存,确认返回吗?',
      })
      .then(() => {
        uni.navigateBack()
      })
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  text-align: right;
  background-color: transparent;
}
::v-deep .custom-class {
  font-size: 30rpx;
  color: #000;
  text-align: right;
}
::v-deep .custom-class-1 {
  display: flex;
  justify-content: center;
  width: 400rpx !important;
  height: 320rpx !important;
  padding: 20rpx 0rpx 20rpx;
  margin: auto;
}
::v-deep .wd-upload__preview {
  width: 100% !important;
  height: 100% !important;
  margin: 0rpx !important;
  object-fit: contain !important;
  border-radius: 20rpx !important;
  box-shadow: 0 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
}

.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 80rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.setting {
  padding: 40rpx 40rpx;

  .setting-card {
    padding: 20rpx 30rpx;
    background-color: #fff;
    border-radius: 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    .img-upload {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 280rpx;
      height: 160rpx;
      margin: 40rpx auto 0rpx;
      background-color: #ededed;
      border-radius: 20rpx;

      .img-upload-icon {
        width: 96rpx;
        height: 96rpx;
        background-image: url('@/resumeRelated/img/zhengshuyanzhen_1.png');
        background-position: 100% 100%;
        background-size: 100% 100%;
      }
    }
  }
}
</style>
