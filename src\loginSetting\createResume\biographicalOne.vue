<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
    'app-plus': {
      //手机软键盘升起不让其将页面头部上推
      softinputMode: 'adjustResize',
    },
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar>
        <template #right>
          <wd-img
            :src="identitySwitchingImg"
            width="45rpx"
            height="45rpx"
            @click="changeIdentFun"
          />
        </template>
      </CustomNavBar>
    </template>

    <view class="page_box">
      <view class="page_padding">
        <view class="page_flex_row">
          <view class="page_flex">
            <view class="page_flex_left">身份创建</view>
            <view class="page_flex_icon">
              <view class="page_flex_img"></view>
            </view>
          </view>
          <view class="page_input text-wrap">
            在线简历将会向企业展示,我们会妥善保护你的隐私,后续你也可以在设置中将简历隐藏～
          </view>
        </view>
        <view class="page_flex_row_bottom flex items-center">
          <view class="tag-name w-100rpx">姓名</view>
          <view class="input-border w-100">
            <wd-input
              type="text"
              :no-border="true"
              v-model="formData.trueName"
              placeholder="请输入你的真实姓名"
              :focus="false"
              :adjust-position="false"
            />
          </view>
        </view>
        <view class="w-100 flex items-center p-t-0rpx">
          <view class="tag-name w-100rpx">性别</view>
          <view class="tag-select-r-list w-100">
            <view
              @click="changeactive(index, item)"
              class="tag-select-r"
              :class="
                index === activeIndex && formData.sex === 1
                  ? 'myStyle-box'
                  : index === activeIndex && formData.sex === 2
                    ? 'myStyle-box-pink'
                    : 'tag-select-r-normal'
              "
              v-for="(item, index) in sexList"
              :key="index"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
        <view class="page_flex_row_bottom flex items-center">
          <view class="tag-name w-200rpx">出生年月</view>
          <view class="month-picker w-100">
            <wd-datetime-picker
              type="year-month"
              v-model="birthdayTimestamp"
              :min-date="minDate"
              :max-date="maxDate"
              :default-value="defaultTime"
              placeholder="请选择出生年月"
            />
            <!-- <month-picker
              mode="date"
              :value="formData.birthday"
              class="picker"
              @change="handlePickerChange"
            ></month-picker>-->
          </view>
        </view>
        <view class="page_flex_row_bottom flex items-center">
          <view class="tag-name w-200rpx">求职状态</view>
          <view class="tag-select-r-list w-100">
            <wd-picker
              custom-view-class="custom-view-class"
              :columns="qzList"
              label=""
              v-model="formData.seekStatus"
            />
            <!-- <view
              @click="changeactiveTwo(index, item)"
              class="tag-select-r-x text-28rpx p-t-20rpx p-b-20rpx"
              :style="index === 2 ? 'width:560rpx' : ''"
              :class="index === activeIndexTwo ? 'myStyle-box text-28rpx' : ''"
              v-for="(item, index) in qzList"
              :key="index"
            >
              {{ item.name }}
            </view> -->
          </view>
        </view>
        <view class="page_flex_row_bottom">
          <view class="tag-name">个人亮点</view>
          <wd-textarea
            :auto-focus="false"
            clear-trigger="focus"
            :adjust-position="false"
            v-model="formData.myLights"
            clearable
            placeholder="请填写您的自我介绍，让企业更快的了解您"
            custom-class="custom-class"
            custom-textarea-container-class="custom-textarea-container-class"
            custom-textarea-class="custom-textarea-class"
          />
        </view>
      </view>
    </view>

    <view class="btn_fixed" @click="submit">
      <view class="btn_box">
        <view class="btn_bg">下一步</view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import { computed, watch, ref } from 'vue'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import monthPicker from '@/components/month-picker/month-picker.vue'
import { userResumeBaseInfo, resumeBaseInfoList } from '@/interPost/biographical'
import { getStatusBar, getCheackInfo } from '@/utils/storege'
import { get50YearsAgoTimestamp } from '@/utils/common'
import identitySwitchingImg from '@/static/mine/business/identity-switching.png'
const { changeIdent } = useChangeIdent()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const formData = reactive({
  trueName: '',
  sex: 1,
  birthday: '',
  seekStatus: 0,
  id: null,
  myLights:
    '我性格活泼开朗，待人真诚热情，对待工作认真负责，具备较强的责任心和执行力。学习能力强，能快速掌握新知识技能，与同事相处融洽，具备良好的团队合作精神和集体荣誉感。',
})

// 用于绑定到 wd-datetime-picker 的时间戳值
const birthdayTimestamp = ref(null)

const activeIndex = ref(0)
const statusBarHeight = ref(0)
const sexList = reactive([
  {
    name: '男',
    value: 1,
  },
  {
    name: '女',
    value: 2,
  },
])
const qzList = reactive([
  {
    label: '离职-随时到岗',
    value: 0,
  },
  {
    label: '在职-考虑机会',
    value: 1,
  },
  {
    label: '观望机会',
    value: 2,
  },
])
const activeIndexTwo = ref(0)

// 切换身份
const changeIdentFun = async () => {
  changeIdent()
}
onLoad(() => {
  statusBarHeight.value = getStatusBar()
})
onShow(() => {
  getInfoList()
})
const getInfoList = async () => {
  await uni.$onLaunched
  const res: any = await resumeBaseInfoList({
    id: getCheackInfo().baseInfoId,
  })
  if (res.code === 0) {
    if (Object.prototype.hasOwnProperty.call(res, 'data')) {
      formData.sex = res.data.sex
      formData.trueName = res.data.trueName
      activeIndex.value = res.data.sex === 1 ? 0 : 1
      // activeIndexTwo.value = res.data.seekStatus === 0 ? 0 : res.data.seekStatus === 1 ? 1 : 2
      formData.birthday = res.data.birthday
      formData.seekStatus = res.data.seekStatus
      formData.myLights = res.data.myLights
    }
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
const submit = async () => {
  if (formData.trueName === '') {
    uni.showToast({
      title: '请填写真实姓名',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (formData.birthday === '') {
    uni.showToast({
      title: '请选择出生年月',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (formData.myLights === '') {
    uni.showToast({
      title: '请输入个人亮点',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  const res: any = await userResumeBaseInfo(formData)

  if (res.code === 0) {
    uni.navigateTo({
      url: '/loginSetting/category/JobIntention',
    })
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 计算50年前的日期作为最小可选日期
const minDate = computed(() => {
  return get50YearsAgoTimestamp()
})

// 计算16年前的日期作为最大可选日期
const maxDate = computed(() => {
  const now = new Date()
  const sixteenYearsAgo = new Date(now.getFullYear() - 16, now.getMonth(), now.getDate())
  return sixteenYearsAgo.getTime()
})

// 默认时间设置为20年前（在范围内）
const defaultTime = computed(() => {
  const now = new Date()
  const twentyYearsAgo = new Date(now.getFullYear() - 20, now.getMonth(), now.getDate())
  return twentyYearsAgo.getTime()
})

// 监听时间戳变化，转换为 yyyy-mm 格式
watch(birthdayTimestamp, (newValue) => {
  if (newValue) {
    const date = new Date(newValue)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    formData.birthday = `${year}-${month}`
  }
})

// 初始化时，如果 formData.birthday 有值，转换为时间戳
watch(
  () => formData.birthday,
  (newValue) => {
    if (newValue && !birthdayTimestamp.value) {
      const [year, month] = newValue.split('-')
      if (year && month) {
        const date = new Date(parseInt(year), parseInt(month) - 1, 1)
        birthdayTimestamp.value = date.getTime()
      }
    }
  },
  { immediate: true },
)

const changeactive = (index: any, item: any) => {
  activeIndex.value = index
  formData.sex = item.value
}
</script>

<style lang="scss" scoped>
::v-deep .u-input__content__field-wrapper__field {
  height: 70rpx !important;
}

::v-deep .u-line-progress__line {
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%) !important;
}
::v-deep .custom-class {
  background: #f7f7f7;
  border-radius: 20rpx !important;
}
::v-deep .custom-textarea-container-class {
  height: 230rpx;
  background: #f7f7f7;
}
::v-deep .wd-textarea__inner {
  height: 230rpx;
}
::v-deep .wd-picker__cell {
  width: 100% !important;
  padding: 10rpx 20rpx;
  border: 1rpx solid #e8e8e8 !important;
  border-radius: 8rpx;
}
.page_flex_left {
  position: relative;
  padding-bottom: 10rpx;
  font-size: 46rpx;
  font-weight: 600;
  color: #000000;
}
.page_flex_left::after {
  position: absolute;
  bottom: 5rpx;
  left: 0rpx;
  width: 200rpx;
  height: 8rpx;
  content: '';
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 32rpx;
}
.wd-picker {
  width: 100% !important;
}
.page_flex_icon {
  position: fixed;
  top: 60rpx;
  right: 60rpx;

  .page_flex_img {
    z-index: 1001;
    width: 350rpx;
    height: 350rpx;
    background-repeat: no-repeat;
    background-position: 100% 100%;
    background-size: 100% 100%;
    @include graph-img('/static/img/Mask_group');
  }
}
.tag-select-r-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
.input-border {
  padding: 10rpx 20rpx !important;
  border: 1rpx solid #e8e8e8;
}
.month-picker {
  padding: 0rpx 0rpx;
}

.myStyle-box-pink {
  background: rgba(253, 239, 239, 1);
  border: 1rpx solid rgba(255, 190, 190, 1);
}
.btn_fixed {
  box-sizing: border-box;
  width: 100%;
  // padding: 0rpx 50rpx;
  margin-bottom: 100rpx;
  .btn_box {
    width: 100%;
    padding: 0rpx 50rpx 0rpx;
    padding-bottom: 0;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.page_flex_for {
  display: flex;
  flex-direction: row;

  .page_flex_list {
    display: flex;
    flex-direction: row;
    margin-left: 20rpx;

    .page_select_border {
      padding: 0 22rpx;
      font-size: 22rpx;
      line-height: 44rpx;
      color: #3e9cff;
      background: #f1f1ff;
      border: 1px solid #3e9cff;
      border-radius: 5px 5px 5px 5px;
    }

    .page_border {
      padding: 0 22rpx;
      font-size: 22rpx;

      line-height: 44rpx;
      color: #777777;
      background: #f5f5f5;
      border-radius: 5px 5px 5px 5px;
    }
  }
}

.input_font {
  font-size: 22rpx;
  font-weight: 500;
  line-height: 44rpx;
  color: #777777;
}

.page_box {
  box-sizing: border-box;
  width: 100%;
  padding: 50rpx;
  padding-top: 140rpx;
}

.page_flex_left_just {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.page_flex_row_bottom {
  width: 100%;
  padding-top: 20rpx;
  padding-bottom: 10rpx;
}

.tag-name {
  padding-bottom: 10rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
}

.tag-select-r-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tag-select-r {
  width: 220rpx;
  padding: 10rpx 0rpx;
  margin: 20rpx 0rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #000;
  text-align: center;

  border-radius: 10rpx;
}
.tag-select-r-x {
  width: 270rpx;
  padding: 15rpx 0rpx;
  margin: 20rpx 0rpx;
  font-weight: 500;
  color: #000;
  text-align: center;
  background-color: #f2f2f2;
  border: 1rpx solid transparent;
  border-radius: 10rpx;
}

.tag-select-r-normal {
  background-color: #f2f2f2;
}
.myStyle-box {
  color: #3e9cff;
  background-color: #f1f1ff;
  border: 1rpx solid #3e9cff;
}

.page_padding {
  position: relative;
  padding: 50rpx 50rpx 50rpx;
  background: #ffffff;
  border-radius: 20px 20px 20px 20px;
  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
}

.page_input {
  padding-top: 16rpx;
  font-size: 26rpx;
  color: #666;
}

.page_input1 {
  padding-top: 10rpx;
}

.page_flex_left_row {
  display: flex;
  flex-direction: row;
  align-items: center;

  .page_flex_left_color {
    font-size: 11px;
    font-weight: 500;
    line-height: 22px;
    color: #333333;
  }

  .page_flex_des {
    font-size: 22rpx;
    font-weight: 500;
    line-height: 44rpx;
    color: #888888;
  }
}

.page_flex {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  // .page_flex_right{
  // 	font-weight: 600;
  // 	font-size: 18px;
  // 	color: #000000;
  // 	line-height: 22px;
  // }
}
/* 键盘显示时的样式调整 */
.keyboard-visible {
  .customNavbar {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    left: 0 !important;
    z-index: 9999 !important;
  }

  .page_box {
    padding-bottom: var(--keyboard-height, 0) !important;
  }
}
/* 防止键盘推送页面的通用样式 */
.prevent-keyboard-push {
  position: relative;
  overflow: hidden;
}

.prevent-keyboard-push .customNavbar {
  position: sticky;
  top: 0;
  z-index: 999;
}
</style>
