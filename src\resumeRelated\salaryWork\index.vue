<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="bg-img">
    <CustomNavBar title="高薪职业"></CustomNavBar>
    <view>
      <view class="page_box">
        <view class="page_flex_row">
          <view class="page_flex">
            <view class="page_flex_left text-32rpx font-500 p-t-40rpx relative">高薪职业</view>
          </view>
          <view class="page_input text-wrap m-t-20rpx">挑战高薪职位，成就职场精英</view>
        </view>
        <view class="page_flex_img"></view>
      </view>
    </view>
    <z-paging
      :fixed="false"
      v-model="pageData"
      @query="queryList"
      ref="pagingRef"
      :paging-style="pageStyle"
      safe-area-inset-bottom
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 150px)` }"
    >
      <view class="salaryWork-box">
        <view class="page_list">
          <view class="page_flex" v-for="(item, index) in pageData" :key="index">
            <view class="page_flex_colom" @click="goDetail(item.id, item.companyId)">
              <view class="page_flex_list">
                <view class="flex-c">
                  <!-- <view class="job-tag" v-if="item.isRecruit === 1">急招</view> -->
                  <wd-img :width="33" :height="16" :src="jz" v-if="item.isRecruit === 1" />
                  <view class="page_left">
                    {{ item.positionName }}
                  </view>
                  <view class="stateType" v-if="item.jobType === 2 || item.jobType === 3">
                    {{ item.jobType === 2 ? '兼职' : item.jobType === 3 ? '实习' : '' }}
                  </view>
                </view>

                <view class="page_right salary">
                  <text>{{ item.workSalaryBegin }}</text>
                  <text v-if="item.workSalaryEnd">-</text>
                  <text v-if="item.workSalaryEnd">{{ item.workSalaryEnd }}</text>
                </view>
              </view>
              <view class="page_flex_list">
                <view class="page_left_1">{{ item.name }}·{{ item.sizeName }}</view>
                <view class="page_right_flex">
                  <wd-icon name="location" size="14px" color="#999"></wd-icon>
                  <view class="page_right_distance">
                    {{ item.distanceMeters ? item.distanceMeters : item.districtName }}
                  </view>
                </view>
              </view>
              <view class="bg_flex">
                <view class="bg_box" v-for="(subName, index) in item.positionKey" :key="index">
                  {{ subName }}
                </view>
              </view>
              <view class="bg_end">
                <view class="bg_left">
                  <image
                    v-if="item.sex === 1"
                    class="bg_left_icon"
                    :src="
                      item.hrPositionUrl ? item.hrPositionUrl : '/static/header/jobhunting1.png'
                    "
                    mode="aspectFill"
                  ></image>
                  <image
                    v-else
                    class="bg_left_icon"
                    :src="
                      item.hrPositionUrl ? item.hrPositionUrl : '/static/header/jobhunting2.png'
                    "
                    mode="aspectFill"
                  ></image>
                  <view class="bg_left_flex">
                    <view class="bg_left_name">
                      {{ item.hrPositionName }}
                      <text v-if="item.hrPosition">·</text>
                      {{ item.hrPosition }}
                    </view>
                    <view class="bg_left_date">{{ item.activityStatus }}</view>
                  </view>
                </view>
                <view class="flex-c">
                  <view class="bg_right m-r-20rpx" @click.stop="goJob">
                    <image
                      class="bg_right_icon"
                      src="/static/img/td-job-card.png"
                      mode="aspectFill"
                    ></image>
                  </view>
                  <view class="bg_right" @click.stop="goChat(item)">
                    <image
                      class="bg_right_icon"
                      src="/static/images/home/<USER>"
                      mode="aspectFill"
                    ></image>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { highPositions } from '@/interPost/resume'
import { numberTokw } from '@/utils/common'
import { getCustomBar } from '@/utils/storege'
import jz from '@/static/img/home/<USER>'
const { getDictLabel } = useDictionary()
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    padding: '60rpx 0rpx 0rpx',
    marginTop: '50rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
  },
})
const { sendGreetingMessage } = useIMConversation()
// 参数
const params = ref({
  entity: {},
  orderBy: {},
})
const customBar = ref(null)
const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res: any = await highPositions({
    ...params.value,
    page: pageInfo.pageNum,
    size: pageInfo.pageSize,
  })
  if (res.code === 0) {
    res.data?.list &&
      res.data.list.forEach(async (ele: any) => {
        ele.sizeName = await getDictLabel(100, ele.sizeName)
        ele.distanceMeters = ele.distanceMeters
          ? Math.floor(parseInt(ele.distanceMeters) / 1000) + 'km'
          : ''
        ele.positionKey = ele.positionKey && ele.positionKey.split(',')
        ele.workSalaryBegin =
          ele.workSalaryBegin === 0 ? '面试' : numberTokw(ele.workSalaryBegin + '')
        ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '' : numberTokw(ele.workSalaryEnd + '')
      })
    pagingRef.value.complete(res.data.list)
  }
}
// 去沟通item
const goChat = (item) => {
  const hxUserInfoVO = item?.hxUserInfoVO || {}
  if (hxUserInfoVO?.username) {
    sendGreetingMessage(hxUserInfoVO.username, item)
  }
}
const goDetail = (id: any, companyId: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${id}&companyId=${companyId}`,
  })
}
const tagList = ref([])
const goJob = () => {
  console.log('ee')
}
const goFilter = () => {
  uni.navigateTo({
    url: '/resumeRelated/filter/index',
  })
}
onLoad(async () => {
  await uni.$onLaunched
  customBar.value = getCustomBar()
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.salary {
  color: #ff8080 !important;
}

.salaryWork-box {
  // height: calc(100vh - 100rpx);
}
.content_list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  .content_list_left {
    // width: 80%;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: calc(100% - 200rpx);
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    .content_list_left_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      // width: 100%;
      padding: 10rpx 30rpx;
      white-space: nowrap;

      .content_list_for {
        display: inline-block;
        margin: auto;

        .content_list_border_1 {
          padding-left: 30rpx;
        }
      }
    }
  }
}
.page_flex_left::after {
  position: absolute;
  bottom: -10rpx;
  left: 0rpx;
  width: 190rpx;
  height: 16rpx;
  content: '';
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 32rpx;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 40rpx 0rpx;
  padding-bottom: 0;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 15rpx 20rpx;
      color: #fff;
      background: rgba(61, 61, 61, 0.34);
      border-radius: 80rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;
        .img-icon {
          width: 46rpx;
          height: 46rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.input_font {
  font-size: 22rpx;
  font-weight: 400;
  line-height: 44rpx;
  color: #777777;
}
.bg_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 100%;
  padding-top: 0rpx !important;
  padding-bottom: 14rpx;

  .bg_box {
    padding: 0rpx 10rpx;
    // margin: 14rpx 0;
    margin-top: 14rpx;
    margin-right: 22rpx;
    font-size: 22rpx;
    font-weight: 400;
    line-height: 44rpx;
    color: #888888;
    background: #f3f3f3;
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}
.bg_end {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-top: 10rpx;
  .bg_right-1 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 132rpx;
    height: 64rpx;
    text-align: center;
    background: #f0f3fd;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    &_icon {
      width: 32rpx;
      height: 32rpx;
    }
  }
  .bg_right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 132rpx;
    height: 64rpx;
    text-align: center;
    background: #fff4f4;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    &_icon {
      width: 50rpx;
      height: 50rpx;
    }
  }

  .bg_left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .bg_left_icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50rpx;
    }

    .bg_left_flex {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-left: 15rpx;

      .bg_left_name {
        font-size: 22rpx;
        font-weight: 400;
        line-height: 44rpx;
        color: #555555;
      }

      .bg_left_date {
        font-size: 20rpx;
        font-weight: 400;
        color: #999999;
        // line-height: 44rpx;
      }
    }
  }
}
.page_box {
  position: relative;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0rpx 40rpx 40rpx;
}

.salary {
  color: #ff8080 !important;
}
.content_list_left-w {
  width: calc(100% - 100rpx);
}
.content_search-p {
  padding: 30rpx 40rpx;
}
.stateType {
  padding: 0rpx 10rpx;
  margin-left: 10rpx;
  font-size: 20rpx;
  color: #888888;
  text-align: center;
  border: 1rpx solid #888888;

  border-radius: 6rpx;
}
.sx {
  background: #adbaff;
}
.jz {
  background: #fda283;
}
.job-tag {
  padding: 0 20rpx;
  margin-right: 10rpx;
  font-size: 24rpx;
  color: #fff;
  background: #ff5151;
  border-radius: 20rpx 0rpx 20rpx 0rpx;
}
.content_search-p-t {
  padding: 0rpx 40rpx 0rpx;
}

.page_left_1 {
  font-size: 22rpx !important;
  font-weight: 400;
  line-height: 44rpx;
  color: #888888;
}
.page_flex_left_just {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.tag-name {
  font-size: 40rpx;
  font-weight: 500;
  color: #000;
}

.tag-select-r-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: left;
}

.tag-select-r {
  padding: 15rpx 40rpx !important;
  margin: 20rpx 30rpx 20rpx 0rpx;
  font-size: 32rpx;
  font-size: 500;
  color: #000;
  text-align: center;
  background-color: #f2f2f2;
  border: 1rpx solid transparent;
  border-radius: 10rpx;
}
.text-28rpx {
  font-size: 28rpx;
}

.page_input {
  width: 320rpx;
  padding-top: 16rpx;
  font-size: 26rpx;
  color: #666;
}

.page_input1 {
  padding-top: 10rpx;
}
.page_list {
  box-sizing: border-box;
  width: 100%;
  padding: 0 40rpx;
  // margin-bottom: 200rpx;

  .page_flex {
    width: 100%;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    .page_flex_colom {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: 100%;

      .page_flex_list {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .page_right_flex {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 5rpx 0;

          .page_left_1 {
            font-size: 22rpx !important;
            font-weight: 400;
            line-height: 44rpx;
            color: #888888;
          }

          .page_right_distance {
            font-size: 22rpx;
            font-weight: 400;
            line-height: 44rpx;
            color: #888888;
          }
        }

        .page_left {
          font-size: 28rpx;
          font-weight: 600;
          line-height: 44rpx;
          color: #333333;
        }

        .page_right {
          font-size: 28rpx;
          font-weight: 600;
          line-height: 44rpx;
          color: #888888;
        }
      }
    }
  }
}
.content_search_list_flex {
  display: flex !important;
  flex-direction: row;
  align-items: center;
  justify-content: space-between !important;
  padding-bottom: 20rpx;
}

.navigation-bar-betwween {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 40rpx;
}

.back-button {
  position: absolute;
  left: 10px;
}

.title {
  font-size: 50rpx;
  font-weight: 500;
  color: #000000;
}
.content_list_right-1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 84rpx;
  height: 84rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
  ._img {
    width: 44rpx;
    height: 44rpx;
  }
}
.content_list_right {
  display: flex;
  flex-direction: row;
  align-items: center;

  .content_list_icon {
    display: flex;
    align-items: center;
    padding: 16rpx 10rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    ._img {
      width: 50rpx;
      height: 50rpx;
    }
  }
}

.content_list_left_for {
  display: flex;
  flex-direction: row;
  // width: 100%;
  padding-right: 50rpx;
}
.page_flex_img {
  position: absolute;
  top: 0rpx;
  right: 40rpx;
  z-index: 10001;
  width: 340rpx;
  height: 300rpx;
  background-image: url('@/resumeRelated/img/tc.png');
  background-repeat: no-repeat;
  background-position: 100% 100%;
  background-size: 100% 100%;
}
.content_list_left_color {
  margin-bottom: -10rpx;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 44rpx;
  color: #000000;
}

.content_list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  .content_list_left {
    position: relative;
    // width: 80%;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: calc(100% - 100rpx);
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    ._img {
      position: absolute;
      right: 10rpx;
      width: 30rpx;
      height: 30rpx;
    }
    .content_list_left_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      // width: 100%;
      padding: 10rpx 20rpx;
      white-space: nowrap;

      .content_list_for {
        display: inline-block;
        margin: auto;

        .content_list_border_1 {
          padding-left: 30rpx;
        }
      }
    }
  }
}

.select_border {
  width: 100%;
  padding: 11rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 44rpx;
  color: #333333;
  text-align: center;
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0rpx 40rpx 0rpx;
  padding-bottom: 0;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 20rpx 30rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;
        ._image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}
</style>
