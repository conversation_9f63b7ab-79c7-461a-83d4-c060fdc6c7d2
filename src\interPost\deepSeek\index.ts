import { POST, POSTPaging } from '@/service'
import { createStreamRequest } from '@/utils/https'

// ai 普通请求
export const chat = (data: any) => {
  return POST('/easyzhipin-ai/aliChat/chat', data)
}

// ai 流式请求
export const chatStream = (
  data: any,
  callbacks: {
    onMessage?: (data: any) => void
    onError?: (error: any) => void
    onComplete?: () => void
    onProgress?: (chunk: string) => void
  },
) => {
  return createStreamRequest({
    url: '/easyzhipin-ai/aliChat/chat',
    data,
    ...callbacks,
  })
}

// 历史记录
export const historyList = (data?: any) => {
  return POSTPaging('/easyzhipin-api/ai/historyList', data)
}
