# 流式聊天实现指南

## 概述

本项目实现了真正的实时流式聊天功能，支持 Server-Sent Events (SSE) 和类似的流式数据传输。

## 核心组件

### 1. 流式请求封装 (`src/utils/https.ts`)

**主要功能：**
- 创建 XMLHttpRequest 流式请求
- 处理实时数据流
- 支持进度回调和错误处理

**关键请求头设置：**
```javascript
xhr.setRequestHeader('Content-Type', 'application/json')
xhr.setRequestHeader('Accept', 'text/event-stream') // 声明接受流式响应
xhr.setRequestHeader('Cache-Control', 'no-cache') // 禁用缓存
xhr.setRequestHeader('Connection', 'keep-alive') // 保持连接
xhr.setRequestHeader('token', token) // 认证token
```

**使用示例：**
```javascript
import { createStreamRequest } from '@/utils/https'

const xhr = createStreamRequest({
  url: '/easyzhipin-ai/aliChat/chat',
  data: { message: 'Hello' },
  onMessage: (data) => {
    console.log('收到数据:', data)
  },
  onProgress: (chunk) => {
    console.log('数据块:', chunk)
  },
  onComplete: () => {
    console.log('传输完成')
  },
  onError: (error) => {
    console.error('传输错误:', error)
  }
})
```

### 2. 流式数据处理器 (`src/utils/streamDataProcessor.ts`)

**StreamDataProcessor 类：**
- 缓冲区管理
- 逐行数据解析
- JSON 和纯文本处理
- 错误恢复机制

**使用示例：**
```javascript
import { StreamDataProcessor } from '@/utils/streamDataProcessor'

const processor = new StreamDataProcessor({
  onMessage: (data) => {
    // 处理解析后的消息
    updateUI(data)
  },
  onComplete: () => {
    // 流式传输完成
  },
  onError: (error) => {
    // 处理错误
  }
})

// 处理数据块
processor.processChunk(chunk)
```

### 3. API 接口封装 (`src/interPost/deepSeek/index.ts`)

**提供两种请求方式：**

1. **普通请求** - 等待完整响应
```javascript
const response = await chat({ message: 'Hello' })
```

2. **流式请求** - 实时数据流
```javascript
const xhr = chatStream(
  { message: 'Hello' },
  {
    onMessage: (data) => { /* 处理消息 */ },
    onError: (error) => { /* 处理错误 */ },
    onComplete: () => { /* 完成回调 */ }
  }
)
```

## 后台数据格式支持

### 1. Server-Sent Events (SSE) 格式
```
data: {"content": "Hello", "type": "text"}

data: {"content": " World", "type": "text"}

data: [DONE]
```

### 2. OpenAI 流式格式
```json
{"choices": [{"delta": {"content": "Hello"}}]}
{"choices": [{"delta": {"content": " World"}}]}
{"choices": [{"delta": {}}]}
```

### 3. 纯文本格式
```
Hello
 World
[DONE]
```

### 4. 自定义 JSON 格式
```json
{"text": "Hello", "timestamp": 1234567890}
{"text": " World", "timestamp": 1234567891}
```

## 实际使用示例

### 在 Vue 组件中使用

```vue
<script setup>
import { ref } from 'vue'
import { chatStream } from '@/interPost/deepSeek/index'

const messages = ref([])
const isStreaming = ref(false)

const sendMessage = async (userInput) => {
  isStreaming.value = true
  
  // 添加用户消息
  messages.value.push({
    content: userInput,
    isUser: true,
    time: new Date().toLocaleTimeString()
  })
  
  // 添加AI消息占位符
  const aiMessageIndex = messages.value.length
  messages.value.push({
    content: '',
    isUser: false,
    time: new Date().toLocaleTimeString()
  })
  
  let accumulatedContent = ''
  
  // 发起流式请求
  chatStream(
    { message: userInput },
    {
      onMessage: (data) => {
        // 提取内容
        let content = extractContent(data)
        if (content) {
          accumulatedContent += content
          messages.value[aiMessageIndex].content = accumulatedContent
        }
      },
      onComplete: () => {
        isStreaming.value = false
      },
      onError: (error) => {
        console.error('流式传输错误:', error)
        messages.value[aiMessageIndex].content = '回答时出现错误'
        isStreaming.value = false
      }
    }
  )
}

// 提取内容的辅助函数
const extractContent = (data) => {
  if (typeof data === 'string') return data
  if (data.content) return data.content
  if (data.choices?.[0]?.delta?.content) return data.choices[0].delta.content
  if (data.text) return data.text
  return ''
}
</script>
```

## 错误处理和重连机制

### 1. 网络错误处理
```javascript
xhr.onerror = function(event) {
  console.error('网络请求失败')
  // 可以实现重连逻辑
  setTimeout(() => {
    retryRequest()
  }, 3000)
}
```

### 2. 超时处理
```javascript
xhr.timeout = 300000 // 5分钟超时
xhr.ontimeout = function() {
  console.error('请求超时')
  // 处理超时逻辑
}
```

### 3. 手动停止流式传输
```javascript
const stopStreaming = () => {
  if (xhr) {
    xhr.abort()
    xhr = null
  }
}
```

## 性能优化建议

### 1. 缓冲区管理
- 及时清理已处理的数据
- 避免缓冲区无限增长

### 2. UI 更新优化
- 使用 `requestAnimationFrame` 控制更新频率
- 批量更新 DOM 操作

### 3. 内存管理
- 组件卸载时清理 XMLHttpRequest
- 避免内存泄漏

## 兼容性说明

### 支持的平台
- ✅ H5 (Web)
- ✅ App (iOS/Android)
- ✅ 微信小程序
- ✅ 支付宝小程序

### 注意事项
- 小程序平台可能需要额外的域名配置
- App 端需要确保网络权限配置正确
- 某些平台可能对长连接有时间限制

## 调试技巧

### 1. 开启详细日志
```javascript
console.log('收到数据块:', chunk)
console.log('解析后数据:', parsedData)
```

### 2. 网络面板监控
- 查看请求头是否正确设置
- 监控数据传输过程
- 检查响应状态码

### 3. 错误边界处理
```javascript
try {
  const data = JSON.parse(content)
  onMessage(data)
} catch (error) {
  console.warn('JSON解析失败，作为纯文本处理:', content)
  onMessage({ content, type: 'text' })
}
```

## 总结

通过以上实现，你可以获得：

1. **真正的实时流式输出** - 数据到达即显示
2. **良好的用户体验** - 类似 ChatGPT 的打字效果
3. **健壮的错误处理** - 网络异常自动恢复
4. **跨平台兼容性** - 支持多种小程序和App平台
5. **易于扩展** - 模块化设计，便于定制

这套方案可以完美替代之前的模拟流式输出，提供真正的实时数据流处理能力。
