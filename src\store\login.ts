import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useLoginStore = defineStore(
  'login',
  () => {
    const cityObj = ref({}) // 城市
    const positionObj = ref({}) // 职位
    const jobObj = ref<AnyArray>([]) // 行业
    const jobObjOnline = ref<AnyObject>({}) // 行业
    // 首页岗位下标
    const homeJobAvtive = ref(null)
    // 首页地址选择
    const homeCity1 = ref<AnyObject>({})
    const homeCity2 = ref<AnyObject>({})
    const homeCity3 = ref<AnyObject>({})

    const setCity = (data: any) => {
      cityObj.value = data
    }
    const setpositionData = (data: any) => {
      positionObj.value = data
    }
    const setjobArry = (data: any) => {
      jobObj.value = data
    }
    const setjobObjOnline = (data: any) => {
      jobObjOnline.value = data
    }
    // 存储首页地址
    const sethomeJobAvtive = (data: any) => {
      homeJobAvtive.value = data
    }
    // 存储首页地址
    const sethomeCity1 = (data: any) => {
      homeCity1.value = data
    }
    const sethomeCity2 = (data: any) => {
      homeCity2.value = data
    }
    const sethomeCity3 = (data: any) => {
      homeCity3.value = data
    }
    return {
      cityObj,
      positionObj,
      jobObj,
      setCity,
      setpositionData,
      setjobArry,
      setjobObjOnline,
      jobObjOnline,
      homeCity1,
      homeCity2,
      homeCity3,
      sethomeCity1,
      sethomeCity2,
      sethomeCity3,
      homeJobAvtive,
      sethomeJobAvtive,
    }
  },
  {
    persist: true,
  },
)
