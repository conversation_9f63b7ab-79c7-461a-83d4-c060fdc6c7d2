<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="h-screen flex flex-col bg-img">
    <CustomNavBar title="期望行业">
      <template #right>
        <view class="qr-btn" @click="submitPage">确定</view>
      </template>
    </CustomNavBar>
    <view class="flex-shrink-0 px-46rpx border-b-1 border-gray-200">
      <view class="content_flex">
        <view class="content_search">
          <view class="content_search_bg">
            <view class="content_search_left">
              <image src="/static/img/search.png" mode="aspectFill"></image>
            </view>
            <view class="content_search_right">
              <wd-input
                no-border
                placeholder="搜索您想要的内容"
                v-model="keyword"
                confirm-type="search"
                @confirm="confirmSearch"
              ></wd-input>
            </view>
          </view>
        </view>
      </view>
      <view v-if="selectedItems.length" class="grid grid-cols-3 gap-20rpx pb-30rpx">
        <view
          v-for="(item, index) in selectedItems"
          :key="index"
          class="tag-select myStyle-box text-28rpx"
          @click="toggleSelect(item)"
        >
          {{ item.name }}
        </view>
      </view>
    </view>
    <view class="flex flex-1 min-h-0">
      <scroll-view scroll-y class="w-230rpx flex-shrink-0">
        <view v-for="(parent, pIndex) in industryList" :key="pIndex">
          <view
            :class="[
              activeFIndex === pIndex ? 'activeBg' : 'normalBg',
              pIndex === activeFIndex - 1 ? 'prev-selected' : '',
              pIndex === activeFIndex + 1 ? 'next-selected' : '',
              'page-list-left-text',
            ]"
            class="page-list-left-text"
            @click="activeF(pIndex)"
          >
            {{ parent.name }}
          </view>
        </view>
      </scroll-view>
      <scroll-view scroll-y class="flex-1 min-w-0">
        <view>
          <view class="page-list-right-p">
            <view class="page-tag-list">
              <view
                v-for="(grandchild, cIndex) in currentChildren"
                :key="cIndex"
                class="tag-select-r"
                :class="grandchild.active ? 'myStyle-box' : ''"
                @click="selectIndustry(cIndex)"
              >
                {{ grandchild.name }}
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { CommonUtil } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import industryDataList from '@/utils/json/industry.json'
import { useLoginStore } from '@/store'

const loginStore = useLoginStore()
const keyword = ref('')
const activeFIndex = ref(0)
const industryList = ref([])
const originalIndustryList = ref([])
const isSearching = ref(false)

const currentChildren = computed(() => {
  return industryList.value[activeFIndex.value]?.childerIndustryData || []
})

const industrySearch = (searchKeyword: string) => {
  if (!searchKeyword.trim()) return originalIndustryList.value

  const keyword = searchKeyword.toLowerCase().trim()
  const results = []

  originalIndustryList.value.forEach((category) => {
    const categoryMatches = category.name.toLowerCase().includes(keyword)
    const matchedIndustries =
      category.childerIndustryData?.filter((industry) =>
        industry.name.toLowerCase().includes(keyword),
      ) || []

    if (categoryMatches || matchedIndustries.length > 0) {
      results.push({
        ...category,
        childerIndustryData: categoryMatches ? category.childerIndustryData : matchedIndustries,
        _matchType: categoryMatches ? 'category' : 'industry',
      })
    }
  })

  return results.sort((a, b) => {
    if (a._matchType === 'category' && b._matchType !== 'category') return -1
    if (b._matchType === 'category' && a._matchType !== 'category') return 1
    return 0
  })
}

const confirmSearch = () => {
  const searchKeyword = keyword.value.trim()
  if (searchKeyword) {
    isSearching.value = true
    const searchResults = industrySearch(searchKeyword)
    industryList.value = searchResults
    if (searchResults.length > 0) {
      activeFIndex.value = 0
    }
  } else {
    clearSearch()
  }
}

const clearSearch = () => {
  isSearching.value = false
  industryList.value = originalIndustryList.value
  keyword.value = ''
  restoreSelectedState()
}

watch(keyword, (newKeyword) => {
  if (!newKeyword.trim() && isSearching.value) {
    clearSearch()
  }
})

const selectedItems = computed(() => {
  const items = []
  for (const parent of industryList.value) {
    for (const industry of parent.childerIndustryData || []) {
      if (industry.active) items.push(industry)
    }
  }
  return items
})

const activeF = (index) => {
  activeFIndex.value = index
}

const resetAllActive = () => {
  for (const parent of industryList.value) {
    for (const child of parent.childerIndustryData || []) {
      child.active = false
    }
  }
}

const selectIndustry = (cIndex) => {
  const currentCategory = industryList.value[activeFIndex.value]
  const targetIndustry = currentCategory.childerIndustryData[cIndex]

  const hasUnlimitedIndustry = selectedItems.value.some((item) => item.code === 0)
  if (hasUnlimitedIndustry && targetIndustry.code !== 0) {
    uni.showToast({ title: '您已选择不限行业了', icon: 'none' })
    return
  }

  // 切换选中状态
  if (targetIndustry.active) {
    targetIndustry.active = false
    return
  }

  // 检查选择限制
  if (selectedItems.value.length >= 3) {
    uni.showToast({ title: '最多只能选择3个行业', icon: 'none' })
    return
  }

  // 特殊处理"不限行业"
  if (currentCategory.code === 0) {
    loginStore.setjobArry([{ name: targetIndustry.name, code: targetIndustry.code }])
    uni.navigateBack()
    return
  }

  targetIndustry.active = true
}

// 简化方法
const toggleSelect = (item) => (item.active = !item.active)

const submitPage = () => {
  loginStore.setjobArry(selectedItems.value)
  uni.navigateBack()
}

// 优化状态恢复
const restoreSelectedState = () => {
  const savedJobs = loginStore.jobObj || []
  if (!savedJobs.length) return

  resetAllActive()
  const savedCodes = new Set(savedJobs.map((job) => job.code))
  let lastMatchIndex = 0

  industryList.value.forEach((category, categoryIndex) => {
    category.childerIndustryData?.forEach((industry) => {
      if (savedCodes.has(industry.code)) {
        industry.active = true
        lastMatchIndex = categoryIndex
      }
    })
  })

  activeFIndex.value = lastMatchIndex
}

const initializeIndustryData = () => {
  return CommonUtil.deepClone(industryDataList.industryData)
}

onLoad(() => {
  const initializedData = initializeIndustryData()
  originalIndustryList.value = initializedData
  industryList.value = initializedData
  restoreSelectedState()
})
</script>

<style lang="scss">
:deep(.wd-input) {
  width: 100%;
  background-color: transparent !important;
  .wd-input__placeholder {
    font-size: 28rpx !important;
    color: #fff !important;
  }
  .wd-input__inner {
    font-size: 28rpx !important;
    font-weight: 500;
    color: #fff !important;
  }
}
.qr-btn {
  width: 100rpx;
  padding: 5rpx 0rpx;
  font-size: 28rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 10rpx;
}

.containner-select-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 20rpx 0;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 40rpx 0 30rpx;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 15rpx 30rpx;
      color: #fff;
      background: rgba(61, 61, 61, 0.34);
      border-radius: 80rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 10%;

        image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.myStyle-box::after {
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  width: 32rpx;
  height: 28rpx;
  content: '';
  background-image: url('../img/Mask_group(2).png');
}

.page-list-right-title {
  padding-bottom: 25rpx;
  font-weight: bold;
}

.page-list-right-p {
  padding: 30rpx 20rpx 0rpx !important;
}

.activeBg {
  font-weight: 500;
  color: #1160ff;
  background: transparent;
}

.tag-select {
  position: relative;
  width: 200rpx;
  padding: 8rpx 0;
  color: #555;
  text-align: center;
  background-color: #fff;
  border: 1px solid #1160ff;
  border-radius: 10rpx;
}

.normalBg {
  background: #e8e8e8;
}

.u-searchs {
  padding: 0 46rpx;
  border-bottom: 1rpx solid $uni-border-b-color;
}

.page-list {
  display: flex;
  padding: 0;

  &-left {
    width: 230rpx;

    &-text {
      padding: 30rpx 10rpx 30rpx 10rpx;
      font-size: 28rpx;
      text-align: center;
      transition: all 0.3s;

      // 相邻元素的圆角
      &.prev-selected {
        border-bottom-right-radius: 20rpx;
      }

      &.next-selected {
        border-top-right-radius: 20rpx;
      }
    }
  }

  &-right {
    flex: 1;

    &-p {
      padding: 40rpx 30rpx;

      &-title {
        margin-bottom: 20rpx;
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }
}

.page-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  align-items: center;
  justify-content: space-between;

  .tag-select-r {
    position: relative;
    width: 225rpx;
    padding: 12rpx 4rpx !important;
    margin-bottom: 10rpx;
    font-size: 28rpx;
    color: #333;
    text-align: center;
    background: #f5f5f5;
    border-radius: 10rpx;
    transition: all 0.3s;

    &.myStyle-box {
      border: 1px solid #1160ff;
    }
  }
}
</style>
