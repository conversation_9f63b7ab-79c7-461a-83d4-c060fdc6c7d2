<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="conpany-img">
    <z-paging :refresher-enabled="false" :loading-more-enabled="false">
      <template #top>
        <CustomNavBar color="#fff">
          <template #right>
            <wd-img
              :width="22"
              :height="22"
              :src="collect"
              v-if="listObj.collectStatus === 0"
              @click="collectFun"
            />
            <wd-img
              :width="22"
              :height="22"
              :src="noCollect"
              v-if="listObj.collectStatus === 1"
              @click="nocollectFun"
            />
          </template>
        </CustomNavBar>
      </template>
      <view class="company-list">
        <view class="flex-c">
          <!-- <image src="/static/img/1.jpg" class="company-logos"></image> -->
          <image
            class="company-logos"
            :src="listObj.companyLogoUrl ? listObj.companyLogoUrl : '/static/header/logo.png'"
            mode="aspectFill"
          ></image>
          <view class="company-title">
            <view class="company-name">{{ listObj.companyName }}</view>
            <view class="flex-c">
              <!-- <view class="white-color font-style p-r-10rpx text-22rpx">B轮融资</view> -->
              <view class="white-color font-style p-r-10rpx text-22rpx">
                {{ listObj.industryName }}
              </view>
              <view class="white-color company-num font-style text-22rpx">
                {{ listObj.sizeName }}
              </view>
            </view>
            <!-- <view class="flex-c"> -->
            <!-- <view class="white-color text-22rpx p-r-10rpx">B互联网</view> -->
            <!-- <view class="white-color text-22rpx">{{ listObj.industryName }}</view> -->
            <!-- </view> -->
          </view>
        </view>
        <view class="company-js">
          <wd-tabs
            v-model="tab"
            class="transparent-tabs"
            color="#F4BB86"
            inactiveColor="#fff"
            lineWidth="76rpx"
          >
            <!-- 公司介绍 -->
            <wd-tab title="公司介绍" name="公司介绍">
              <view class="tab-content">
                <view class="text-container">
                  <text class="text-content white-color text-24rpx">{{ text }}</text>
                  <text
                    v-if="showMore"
                    class="view-detail text-24rpx"
                    @click="showDetail"
                    style="color: #f4bb86"
                  >
                    查看详情
                  </text>
                  <text
                    v-if="showText && !showMore"
                    class="view-detail text-24rpx"
                    @click="showShrink"
                    style="color: #f4bb86"
                  >
                    收起
                  </text>
                </view>
              </view>
            </wd-tab>

            <!-- 福利待遇 -->
            <wd-tab title="福利待遇" name="福利待遇">
              <view class="tab-content company-benefit">
                <view class="benefit-grid">
                  <view
                    class="benefit-grid-item"
                    v-for="(item, index) in displayedBenefits"
                    :key="index"
                  >
                    <view class="flex-c">
                      <image :src="listSwiper[index]" class="benefit-list-img m-r-20rpx"></image>
                      <view class="white-color benefit-list-name text-28rpx">
                        {{ item.welfareTreatment }}
                      </view>
                    </view>
                  </view>
                </view>
                <!-- 查看详情/收起按钮 -->
                <view
                  v-if="listObj.companyWelfareVOList && listObj.companyWelfareVOList.length > 4"
                  class="benefit-toggle-btn"
                  @click="toggleBenefits"
                >
                  <text class="toggle-text">{{ benefitsExpanded ? '收起' : '查看更多' }}</text>
                  <text class="toggle-icon">{{ benefitsExpanded ? '▲' : '▼' }}</text>
                </view>
              </view>
            </wd-tab>

            <!-- 公司照片 -->
            <wd-tab title="公司照片" name="公司照片">
              <view class="tab-content">
                <view class="card-swiper">
                  <wd-swiper
                    :autoplay="false"
                    v-model:current="current"
                    custom-indicator-class="custom-indicator-class"
                    custom-image-class="custom-image"
                    custom-next-image-class="custom-image-prev"
                    custom-prev-image-class="custom-image-prev"
                    :indicator="{ type: 'none' }"
                    :list="listObj.companyStyleVOLists"
                    value-key="attachIdUrl"
                    previousMargin="44px"
                    nextMargin="44px"
                    height="260rpx"
                  ></wd-swiper>
                </view>
              </view>
            </wd-tab>

            <!-- 工商信息 -->
            <wd-tab title="工商信息" name="工商信息">
              <view class="tab-content">
                <view class="white-color line-20 text-28rpx">
                  公司全称：{{ listObj.companyName }}
                </view>
                <view class="white-color line-20 text-28rpx">
                  法定代表人：{{ listObj.legalPerson }}
                </view>
                <view class="white-color line-20 text-28rpx">
                  注册资本：{{ listObj.registeredCapital }}
                </view>
                <view class="white-color line-20 text-28rpx">
                  成立日期：{{ listObj.registrationDate }}
                </view>
              </view>
            </wd-tab>
          </wd-tabs>
        </view>
        <view class="company-adress p-b-20rpx">
          <view class="text-32rpx white-color">公司地址</view>
          <view class="flex-between">
            <view class="white-color text-24rpx address-container">
              <view class="address-line">{{ listObj.regAddress }}</view>
            </view>
            <view class="m-l-20rpx" @click="goMap">
              <view class="company-adress-img">
                <view class="company-adress-icon"></view>
              </view>
              <!-- <view class="white-color text-c text-22rpx p-t-10rpx">导航</view> -->
            </view>
          </view>
        </view>
      </view>
      <view class="company-list company-list-job m-t--20rpx">
        <view class="flex-between p-b-20rpx">
          <view class="text-32rpx white-color">更多岗位</view>
          <view class="view-all-btn" @click="goAllJobs">
            <text class="view-all-text">查看全部</text>
            <text class="view-all-arrow">
              <wd-icon
                color="#B6B6B6"
                name="chevron-right"
                size="15px"
                class="arrow-right-1"
              ></wd-icon>
            </text>
          </view>
        </view>
        <scroll-view scroll-x class="job-scroll-view">
          <view class="job-list">
            <view
              class="job-card"
              v-for="(item, index) in jobList"
              :key="index"
              @click="goJobDetail(item.id)"
            >
              <view class="job-card-content">
                <view class="job-info-row">
                  <image :src="jobListIcon[0]" class="job-icon"></image>
                  <view class="job-title white-color text-28rpx">
                    {{ item.positionName || '视频运营总监电商运营' }}
                  </view>
                </view>
                <view class="job-info-row">
                  <image :src="jobListIcon[1]" class="job-icon"></image>
                  <view class="job-salary white-color text-24rpx">
                    <template v-if="!item.workSalaryBegin || !item.workSalaryEnd">面议</template>
                    <template v-else>
                      {{ formatSalary(item.workSalaryBegin) }}-{{
                        formatSalary(item.workSalaryEnd)
                      }}
                      <text v-if="item.salaryMonths">·{{ item.salaryMonths }}薪</text>
                    </template>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { getCustomBar } from '@/utils/storege'
import { numberTokw } from '@/utils/common'
import {
  queryAllDetailId,
  cancelCompany,
  collectCompany,
  queryPositionByCompanyId,
} from '@/interPost/home'
import { baseUrlPrever } from '@/interPost/img'

import listSwiperOne from '@/static/img/1.jpg'
import listSwiperOne1 from '@/resumeRelated/img/Union-21_1.png'
import listSwiperOne2 from '@/resumeRelated/img/rili_1.png'
import collect from '@/static/img/collect.png'
import noCollect from '@/static/img/noCollect.png'
import salaryIcon from '@/static/img/salary_icon.png'
import toolkitIcon from '@/static/img/toolkit_icon.png'

const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const { getDictLabel } = useDictionary()

// 公司id
const id = ref(null)
// 主键id
const idMain = ref(null)
// 列表
const listObj = ref({})
// 岗位列表
const jobList = ref<any>([])
const content = ref(
  '重庆某某某科技股份有限公司创建于2015年，位于重庆市渝北区华润大厦49楼4901，注册资金1000万，在职总共员工200余人，同时在深圳、成都、福州等地区都设有分公司',
)
const customBar = ref(0)
const circular = ref(true)
const current = ref(1)
const text = ref('') // 剪切之后的文字
const textyl = ref('') // 原来的字符
const showMore = ref(false) // 判断显示查看或者收起   小于40字不展示
const textlength = ref(false) // 判断是否点击详情 true为点击详情 false为点击收起
const showText = ref(false) // 收起文字的显示隐藏
const lon = ref(null)
const lat = ref(null)
const listSwiper = ref([listSwiperOne1, listSwiperOne2, listSwiperOne2, listSwiperOne1])
const jobListIcon = ref([toolkitIcon, salaryIcon])
const tab = ref('公司介绍') // 当前选中的标签页索引

// 福利待遇展开/收起相关
const benefitsExpanded = ref(false) // 福利待遇是否展开

// 格式化薪资，将元转换为K单位
const formatSalary = (salary: number) => {
  if (salary === 0) return '面议'
  return numberTokw(salary)
}

// 计算显示的福利待遇列表
const displayedBenefits = computed(() => {
  if (!listObj.value.companyWelfareVOList) return []

  const benefits = listObj.value.companyWelfareVOList
  if (benefits.length <= 4) {
    return benefits
  }

  return benefitsExpanded.value ? benefits : benefits.slice(0, 4)
})

// 切换福利待遇展开/收起状态
const toggleBenefits = () => {
  benefitsExpanded.value = !benefitsExpanded.value
}

// 获取列表
const getList = async () => {
  const res: any = await queryAllDetailId({
    id: id.value,
  })
  if (res.code === 0) {
    textyl.value = res.data.profile // 保存原始文本
    res.data.sizeName = await getDictLabel(100, res.data.sizeName)
    listObj.value = res.data
    lon.value = res.data.lon
    lat.value = res.data.lat
    // 在获取数据后立即检查文本长度并设置显示内容
    checkTextLength()
  }
}
// 获取全部岗位
const getAllJobs = async () => {
  const res: any = await queryPositionByCompanyId({
    entity: {
      id: id.value,
      positionName: '',
    },
    size: pageInfo.pageSize,
    page: pageInfo.pageNum,
  })
  // 处理岗位数据
  if (res.code === 0) {
    jobList.value = res.data.list.slice(0, 4) || []
  }
}
// 收藏
const collectFun = async () => {
  const res: any = await collectCompany({ id: id.value })
  if (res.code === 0) {
    listObj.value.collectStatus = 1
  }
}
// 取消收藏
const nocollectFun = async () => {
  const res: any = await cancelCompany({ id: id.value })
  if (res.code === 0) {
    listObj.value.collectStatus = 0
  }
}
const goMap = () => {
  uni.openLocation({
    latitude: Number(lat.value), // 目标纬度
    longitude: Number(lon.value), // 目标经度
    name: listObj.value.companyName, // 显示在地图上的标记名称
    address: listObj.value.regAddress, // 辅助信息
    success: () => console.log('跳转成功'),
    fail: (err) => console.error('跳转失败', err),
  })
}

// 跳转到岗位详情
const goJobDetail = (jobId: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${jobId}&companyId=${id.value}`,
  })
}

// 查看全部岗位
const goAllJobs = () => {
  uni.navigateTo({
    url: `/resumeRelated/moreJobs/index?companyId=${id.value}`,
  })
}
onLoad(async (options) => {
  await uni.$onLaunched
  customBar.value = getCustomBar()
  id.value = options.companyId
  idMain.value = options.id
  getList()
  getAllJobs()
  // checkTextLength() 已经在 getList() 中调用，这里不需要重复调用
})
const checkTextLength = () => {
  // 如果原始文本长度大于95字符
  if (textyl.value && textyl.value.length > 95) {
    if (!textlength.value) {
      // 默认显示简洁内容（前95个字符 + ...）
      text.value = textyl.value.substring(0, 95) + '...'
      showMore.value = true // 显示"查看详情"按钮
      showText.value = false // 隐藏"收起"按钮
    } else {
      // 显示完整内容
      text.value = textyl.value
      showMore.value = false // 隐藏"查看详情"按钮
      showText.value = true // 显示"收起"按钮
    }
  } else {
    // 文本长度不超过95字符，直接显示全部内容，不显示任何按钮
    text.value = textyl.value
    showMore.value = false
    showText.value = false
  }
}
const showDetail = () => {
  if (showMore.value) {
    textlength.value = true
    checkTextLength()
  }
}
const showShrink = () => {
  if (showText.value) {
    textlength.value = false
    checkTextLength()
  }
}
</script>

<style scoped lang="scss">
.card-swiper {
  --wot-swiper-radius: 0;
  --wot-swiper-item-padding: 0 24rpx;
  --wot-swiper-nav-dot-color: #e7e7e7;
  --wot-swiper-nav-dot-active-color: #4d80f0;
  padding-bottom: 24rpx;
  :deep(.custom-indicator-class) {
    bottom: -16px;
  }
  :deep(.custom-image) {
    border-radius: 20rpx;
  }
  :deep(.custom-image-prev) {
    // height: 168px !important;
  }
}
.view-detail {
  color: #fff;
}
.conpany-img {
  width: 100%;
  min-height: 100vh;
  background-image: url('/static/img/Mask_group(25).png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.uScrollList-scroll-view {
  white-space: nowrap;
}

:deep(.u-read-more__toggle) {
  background-image: none !important;
}

.company-list {
  padding: 40rpx;
  margin-top: 20rpx;

  .company-logos {
    width: 128rpx !important;
    height: 128rpx !important;
    border-radius: 18rpx;
  }

  .company-title {
    flex: 1;
    margin-left: 20rpx;
    color: #fff;

    .company-name {
      font-size: 32rpx;
      font-weight: 500;
      color: #f4bb86;
    }

    .company-num {
      position: relative;
      padding-left: 10rpx;
    }

    .company-num::after {
      position: absolute;
      top: 7rpx;
      left: 0rpx;
      width: 1rpx;
      height: 20rpx;
      content: '';
      background-color: #fff;
    }
  }

  .company-js {
    padding: 60rpx 0 16rpx 0;
  }
  .transparent-tabs {
    top: 20rpx;
    color: #fff;
    background-color: transparent !important; /* 整体背景透明 */

    :deep(.wd-tabs__nav-container) {
      right: 3%; /* 将标题容器靠左对齐 */
    }
    :deep(.wd-tabs__line) {
      width: 76rpx !important;
      background-color: #f4bb86 !important;
    }
    /* 标签页导航项字体大小控制 */
    :deep(.wd-tabs__nav-item) {
      display: flex !important;
      align-items: flex-end !important; /* 垂直对齐到底部 */
      justify-content: center !important;
      padding: 10rpx;
      font-size: 22rpx !important; /* 未选中时的字体大小 */
      transition: font-size 0.3s ease;
    }

    :deep(.wd-tabs__nav-item.is-active) {
      // font-weight: 500;
      display: flex !important;
      align-items: flex-end !important; /* 垂直对齐到底部 */
      justify-content: center !important;
      font-size: 32rpx !important; /* 选中时的字体大小 */
    }

    :deep(.wd-tabs__container) {
      margin: 20rpx 0 30rpx 0;
    }
  }

  .transparent-tabs ::v-deep .wd-tabs__nav {
    background-color: transparent !important; /* 标签栏背景透明 */
  }

  .transparent-tabs ::v-deep .wd-tab__panel {
    background-color: transparent !important; /* 内容区域背景透明 */
  }
  /* 备用选择器 */
  .transparent-tabs ::v-deep .wd-tabs__nav-item {
    display: flex !important;
    align-items: flex-end !important; /* 垂直对齐到底部 */
    justify-content: center !important;
    font-size: 24rpx !important;
  }

  .transparent-tabs ::v-deep .wd-tabs__nav-item.is-active {
    display: flex !important;
    align-items: flex-end !important; /* 垂直对齐到底部 */
    justify-content: center !important;
    font-size: 32rpx !important;
  }

  .company-adress {
    padding: 38rpx 0 0 0;

    .company-adress-img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120rpx;
      height: 60rpx;
      margin-left: 20rpx;
      line-height: 60rpx;
      border: 1rpx solid #f0bb80;
      border-radius: 20rpx;

      .company-adress-icon {
        width: 36rpx;
        height: 36rpx;
        line-height: 60rpx;
        background-image: url('/static/img/daohang_1.png');
        background-position: 100% 100%;
        background-size: 100% 100%;
      }
    }
  }
}

.company-benefit {
  padding: 0 0 0 40rpx;

  .benefit-list {
    .benefit-list-item {
      display: inline-block;
      padding: 30rpx 40rpx;
      margin-right: 30rpx !important;
      background-color: #2b2b2b;
      border-radius: 20rpx;
    }

    .benefit-list-name {
      color: #888;
    }

    .benefit-list-img {
      width: 32rpx !important;
      height: 32rpx !important;
    }
  }
  /* 新增：网格布局样式 - 一行两个框 */
  .benefit-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    padding-right: 40rpx;

    .benefit-grid-item {
      box-sizing: border-box;
      width: calc(50% - 10rpx); /* 一行两个，减去间距 */
      padding: 30rpx 20rpx;
      background-color: #2b2b2b;
      border-radius: 20rpx;

      .benefit-list-name {
        font-size: 28rpx;
        color: #888;
      }

      .benefit-list-img {
        width: 32rpx !important;
        height: 32rpx !important;
      }
    }
  }

  /* 展开/收起按钮样式 */
  .benefit-toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
    // margin-top: 20rpx;
    margin-right: 40rpx;
    // background-color: #2b2b2b;
    border-radius: 20rpx;
    cursor: pointer;
    // transition: background-color 0.3s ease;

    &:hover {
      // background-color: #3a3a3a;
    }

    .toggle-text {
      color: #f4bb86;
      font-size: 28rpx;
      margin-right: 10rpx;
    }

    .toggle-icon {
      color: #f4bb86;
      margin-right: 10rpx;
      font-size: 24rpx;
      transition: transform 0.3s ease;
    }
  }
}
/* 更多岗位样式 */
.job-container {
  padding-bottom: 20rpx;
}

.job-list {
  display: grid;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding-right: 40rpx;
}

.job-card {
  box-sizing: border-box;
  width: calc(50% - 10rpx);
  min-width: 360rpx;
  max-width: 500rpx;
  padding: 20rpx 16rpx;
  cursor: pointer;
  background-color: #2b2b2b;
  border-radius: 20rpx;
  transition: background-color 0.3s ease;

  &:active {
    background-color: #3a3a3a;
  }
}

.job-card-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.job-info-row {
  display: flex;
  align-items: center;
}

.job-icon {
  width: 28rpx !important;
  height: 28rpx !important;
  margin-right: 16rpx;
}

.job-title {
  flex: 1;
  overflow: hidden;
  font-weight: 500;
  line-height: 1.4;
  color: #b6b6b6;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.job-salary {
  flex: 1;
  font-weight: 400;
  color: #f4bb86;
}
/* 查看全部按钮样式 */
.view-all-btn {
  display: flex;
  align-items: center;

  &:active {
    opacity: 0.7;
  }
}

.view-all-text {
  margin-right: 8rpx;
  font-size: 24rpx;
  color: #b6b6b6;
}

.view-all-arrow {
  margin-top: 5rpx;
  font-size: 20rpx;
  font-weight: bold;
  color: #b6b6b6;
}
.company-list-job {
  margin-top: -20rpx;
}
</style>
