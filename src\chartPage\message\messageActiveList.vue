<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" ref="pagingRef" v-model="pageData" @query="queryList">
    <template #top>
      <CustomNavBar title="活动消息"></CustomNavBar>
    </template>
    <view class="contanner-page">
      <view
        class="contanner-page-item"
        v-for="(item, index) in pageData"
        :key="index"
        @click="goActiveDetail(item.id)"
      >
        <image :src="info" mode="scaleToFill" class="contanner-page-item-i" />
        <view class="p-l-20rpx p-r-20rpx">
          <view class="contanner-title font-400 text-28rpx">{{ item.title }}</view>
          <view class="u-line-1 contanner-test text-28rpx c-#555">
            {{ item.description }}
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryActivitiList } from '@/interPost/messege'
import info from '@/chartPage/img/info.png'
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 参数
const params = ref({
  entity: {},
  orderBy: {},
})
// 去活动
const goActiveDetail = (id) => {
  uni.navigateTo({
    url: `/chartPage/message/infoDetail?id=${id}&isShow=active`,
  })
}
onLoad(async () => {
  await uni.$onLaunched
  pagingRef.value.reload()
})

const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res: any = await queryActivitiList({
    ...params.value,
    page: pageInfo.pageNum,
    size: pageInfo.pageSize,
  })
  if (res.code === 0) {
    pagingRef.value.complete(res.data.list)
  }
}
</script>

<style scoped lang="scss">
.contanner-page {
  padding: 40rpx 60rpx 40rpx;
  .contanner-page-item {
    width: 100%;
    margin-bottom: 40rpx;
    background: #fff;
    border-radius: 10rpx;
    box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);

    .contanner-page-item-i {
      width: 100%;
      height: 230rpx;
      border-radius: 10rpx 10rpx 0 0;
    }
    .contanner-title {
      padding-top: 10rpx;
      font-size: 28rpx;
      color: rgba(51, 51, 51, 1);
    }
    .contanner-test {
      padding-bottom: 20rpx;
    }
  }
}
</style>
