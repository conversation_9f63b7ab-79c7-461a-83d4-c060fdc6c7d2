import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useResumeStore = defineStore(
  'resume',
  () => {
    const department = ref('') // 部门
    const company = ref('') // 公司
    const companyId = ref('') // 公司
    const workDescription = ref('') // 工作描述
    const workPerformance = ref('') // 工作业绩
    const skills = ref('') // 工作技能
    const isAdd = ref('')
    const school = ref('') // 学校
    const major = ref('') // 专业
    const projectDescs = ref('') // 项目描述
    const projectPerformance = ref('') // 项目业绩
    // 首页筛选
    const fillterObg = ref<AnyObject>({})
    // 判断首页的onshow是否加载
    const isRefresh = ref(0)
    // 方法
    const setfillterObg = (data) => {
      fillterObg.value = data
    }
    const setDepartment = (data) => {
      department.value = data
    }
    const setCompany = (data) => {
      company.value = data
    }
    const setCompanyId = (data) => {
      companyId.value = data
    }
    const setWorkDescription = (data) => {
      workDescription.value = data
    }
    const setWorkPerformance = (data) => {
      workPerformance.value = data
    }
    const setSkills = (data) => {
      skills.value = data
    }
    const setisAdd = (data) => {
      isAdd.value = data
    }
    const setProjectDescs = (data) => {
      projectDescs.value = data
    }
    const setProjectPerformance = (data) => {
      projectPerformance.value = data
    }
    const setSchool = (data) => {
      school.value = data
    }
    const setMajor = (data) => {
      major.value = data
    }
    const setIsRefresh = (data) => {
      isRefresh.value = data
    }
    return {
      department,
      setCompany,
      company,
      setDepartment,
      workDescription,
      setWorkDescription,
      workPerformance,
      setWorkPerformance,
      setSkills,
      skills,
      isAdd,
      setisAdd,
      projectDescs,
      setProjectDescs,
      projectPerformance,
      setProjectPerformance,
      school,
      setSchool,
      major,
      setMajor,
      companyId,
      setCompanyId,
      fillterObg,
      setfillterObg,
      isRefresh,
      setIsRefresh,
    }
  },
  {
    persist: true,
  },
)
