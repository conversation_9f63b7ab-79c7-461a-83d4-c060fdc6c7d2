<template>
  <view class="month-picker-wrapper">
    <!-- 触发器 -->
    <view class="picker-trigger" @click="showPicker = true">
      <text class="picker-text">{{ displayValue || placeholder }}</text>
      <wd-icon name="chevron-right" size="22px" color="rgba(0, 0, 0, 0.25)"></wd-icon>
    </view>

    <!-- 弹框选择器 -->
    <wd-popup v-model="showPicker" position="bottom" :safe-area-inset-bottom="true">
      <view class="picker-popup">
        <!-- 头部操作栏 -->
        <view class="picker-header">
          <text class="cancel-btn" @click="handleCancel">取消</text>
          <text class="picker-title">选择日期</text>
          <text class="confirm-btn" @click="handleConfirm">确认</text>
        </view>

        <!-- 选择器内容 -->
        <view class="picker-content">
          <picker-view
            class="picker-view"
            :value="tempPickerValue"
            @change="handleTempChange"
            :indicator-style="indicatorStyle"
          >
            <!-- 年份列 -->
            <picker-view-column>
              <view class="picker-item" v-for="year in yearRange" :key="year">{{ year }}年</view>
            </picker-view-column>

            <!-- 月份列（动态生成） -->
            <picker-view-column>
              <view class="picker-item" v-for="month in months" :key="month">
                {{ String(month).padStart(2, '0') }}月
              </view>
            </picker-view-column>
          </picker-view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '请选择日期',
  },
  mode: {
    type: String,
    default: 'date',
  },
})

const emit = defineEmits(['input', 'change'])

// 弹框状态
const showPicker = ref(false)
// 临时选择值（用于确认前的预览）
const tempPickerValue = ref([0, 0])
// 显示值
const displayValue = ref('')

const indicatorStyle = `
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
`

const currentDate = new Date()
const latestDate = new Date()
latestDate.setFullYear(latestDate.getFullYear() - 16)
latestDate.setDate(0)

const startYear = currentDate.getFullYear() - 80
const endYear = latestDate.getFullYear()

const yearRange = Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i)
const latestYear = latestDate.getFullYear()
const latestMonth = latestDate.getMonth() // 0-based
const pickerValue = ref([0, 0])

const months = computed(() => {
  const selectedYear = yearRange[pickerValue.value[0]]
  if (selectedYear < latestYear) {
    return Array.from({ length: 12 }, (_, i) => i + 1) // 1-12
  }
  return Array.from({ length: latestMonth + 1 }, (_, i) => i + 1) // 1-latestMonth+1
})

// 初始化或更新picker值
const updatePickerValue = (year: number, month: number) => {
  const yearIndex = yearRange.findIndex((y) => y === year)
  if (yearIndex === -1) return

  // 确保月份不超过可选范围
  const maxMonth = year === latestYear ? latestMonth + 1 : 12
  const monthIndex = Math.min(month, maxMonth) - 1 // 转换为0-based

  pickerValue.value = [yearIndex, Math.max(0, monthIndex)]
}

const initDefaultValue = () => {
  let year: number, month: number

  if (props.value) {
    const [y, m] = props.value.split('-').map(Number)
    year = y
    month = m
    updateDisplayValue(year, month)
  } else {
    year = latestYear
    month = latestMonth + 1 // 使用1-based月份
  }

  // 验证范围
  if (year > latestYear || (year === latestYear && month > latestMonth + 1)) {
    year = latestYear
    month = latestMonth + 1
  }

  updatePickerValue(year, month)
  // 初始化时不触发 emit，只有用户操作时才触发
}

// 监听弹框打开，同步临时值
watch(showPicker, (newVal) => {
  if (newVal) {
    tempPickerValue.value = [...pickerValue.value]
  }
})

// 临时选择变化（弹框内的选择）
const handleTempChange = (e: any) => {
  const [yearIndex, monthIndex] = e.detail.value
  const selectedYear = yearRange[yearIndex]

  // 确保月份不超过限制
  let adjustedMonthIndex = monthIndex
  if (selectedYear === latestYear) {
    adjustedMonthIndex = Math.min(monthIndex, latestMonth)
  } else {
    adjustedMonthIndex = Math.min(monthIndex, 11)
  }

  tempPickerValue.value = [yearIndex, adjustedMonthIndex]
}

// 确认选择
const handleConfirm = () => {
  pickerValue.value = [...tempPickerValue.value]
  const selectedYear = yearRange[tempPickerValue.value[0]]
  const selectedMonth = tempPickerValue.value[1] + 1

  emitDate(selectedYear, selectedMonth)
  updateDisplayValue(selectedYear, selectedMonth)
  showPicker.value = false
}

// 取消选择
const handleCancel = () => {
  // 恢复到当前值
  tempPickerValue.value = [...pickerValue.value]
  showPicker.value = false
}

// 更新显示值
const updateDisplayValue = (year: number, month: number) => {
  const formattedMonth = String(month).padStart(2, '0')
  displayValue.value = `${year}-${formattedMonth}`
}

const emitDate = (year: number, month: number) => {
  const formattedMonth = String(month).padStart(2, '0')
  const dateStr = `${year}-${formattedMonth}`
  emit('input', dateStr)
  emit('change', dateStr)
}

// 初始化
initDefaultValue()

// 监听外部value变化
watch(
  () => props.value,
  (newVal) => {
    if (!newVal) return

    const [currentYear, currentMonth] = [yearRange[pickerValue.value[0]], pickerValue.value[1] + 1]
    const [newYear, newMonth] = newVal.split('-').map(Number)

    if (currentYear !== newYear || currentMonth !== newMonth) {
      updatePickerValue(newYear, newMonth)
      updateDisplayValue(newYear, newMonth)
    }
  },
)
</script>
<style scoped>
.month-picker-wrapper {
  width: 100%;
}
/* 触发器样式 */
.picker-trigger {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 70rpx;
  padding: 10rpx 20rpx;
  cursor: pointer;
  background: #fff;
  border: 2rpx solid #e4e7ed;
  border-radius: 8rpx;
  transition: border-color 0.2s ease;
}

.picker-trigger:active {
  border-color: #409eff;
}

.picker-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.picker-text:empty::before {
  color: #c0c4cc;
  content: attr(placeholder);
}

.picker-arrow {
  font-size: 32rpx;
  color: #c0c4cc;
  transition: transform 0.2s ease;
  transform: rotate(0deg);
}
/* 弹框样式 */
.picker-popup {
  overflow: hidden;
  background: #fff;
  border-radius: 16rpx 16rpx 0 0;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: #fff;
  border-bottom: 2rpx solid #f5f5f5;
}

.cancel-btn {
  font-size: 28rpx;
  color: #909399;
  cursor: pointer;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.confirm-btn {
  font-size: 28rpx;
  color: #409eff;
  cursor: pointer;
}

.picker-content {
  height: 400rpx;
  background: #fff;
}

.picker-view {
  width: 100%;
  height: 100%;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 32rpx;
  font-weight: normal;
  line-height: 80rpx;
  color: #333;
  text-align: center;
}
</style>
