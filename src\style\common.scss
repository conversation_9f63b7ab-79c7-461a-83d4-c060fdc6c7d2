// 公共的类
// 文字省略
.u-line-1 {
  overflow: hidden; //文本超出隐藏
  text-overflow: ellipsis; //文本超出省略号替代
  white-space: nowrap;
}
.u-line-2 {
  display: -webkit-box; //将盒子转换为弹性盒子
  -webkit-box-orient: vertical; //文本显示方式，默认水平
  -webkit-line-clamp: 2; //设置显示多少行
  overflow: hidden;
  text-overflow: ellipsis;
}
.font-w-300 {
  font-weight: 300;
}
.font-w-400 {
  font-weight: 400;
}
.font-w-500 {
  font-weight: 500;
}
.text-c {
  text-align: center;
}

.text-l {
  text-align: left;
}

.text-r {
  text-align: right;
}
.white-color {
  color: #fff;
}
.dark-color {
  color: #000;
}
.red-color {
  color: #ff0000;
}

// 弹性布局
.flex {
  display: flex;
}

.flex-c {
  display: flex;
  align-items: center;
}
.flex-c-just {
  justify-content: right;
}
//纵向布局
.flex-col {
  flex-direction: column;
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-between-b {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}

.flex-wrap {
  flex-wrap: wrap;
}
// .flex-1 {
//   flex: 1;
// }
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-just {
  justify-content: center;
}

// 边距
.p-10 {
  padding: 10rpx;
}
.p-15 {
  padding: 15rpx;
}
.p-20 {
  padding: 20rpx;
}
.p-b-5 {
  padding-bottom: 5rpx;
}
.p-b-10 {
  padding-bottom: 10rpx;
}
.p-b-15 {
  padding-bottom: 15rpx;
}
.p-b-20 {
  padding-bottom: 20rpx;
}
.p-b-30 {
  padding-bottom: 30rpx;
}
.p-b-40 {
  padding-bottom: 40rpx;
}
.p-t-10 {
  padding-top: 10rpx;
}
.p-t-15 {
  padding-top: 15rpx;
}
.p-t-20 {
  padding-top: 20rpx;
}
.p-t-30 {
  padding-top: 30rpx;
}
.p-t-40 {
  padding-top: 40rpx;
}

.p-r-10 {
  padding-right: 10rpx;
}
.p-r-15 {
  padding-right: 15rpx;
}
.p-r-20 {
  padding-right: 20rpx;
}
.p-r-40 {
  padding-right: 40rpx;
}
.p-l-10 {
  padding-left: 10rpx;
}
.p-l-15 {
  padding-left: 15rpx;
}
.p-l-20 {
  padding-left: 20rpx;
}
.p-l-40 {
  padding-left: 40rpx;
}
.m-l-5 {
  margin-left: 5rpx;
}
.m-l-10 {
  margin-left: 10rpx;
}
.m-l-15 {
  margin-left: 15rpx;
}
.m-l-20 {
  margin-left: 20rpx;
}
.m-r-10 {
  margin-right: 10rpx;
}
.m-r-20 {
  margin-right: 20rpx;
}
.m-r-30 {
  margin-right: 30rpx;
}
.m-t-10 {
  margin-top: 10rpx;
}
.m-t-15 {
  margin-top: 15rpx;
}
.m-t-20 {
  margin-top: 20rpx;
}
.m-t-30 {
  margin-top: 30rpx;
}
.m-t-40 {
  margin-top: 40rpx;
}
.m-b-10 {
  margin-bottom: 10rpx;
}
.m-b-20 {
  margin-bottom: 20rpx;
}
.m-b-30 {
  margin-bottom: 30rpx;
}
.m-b-40 {
  margin-bottom: 40rpx;
}
// 宽度
.w-33 {
  width: 33%;
}
.w-25 {
  width: 25%;
}
.w-20 {
  width: 20%;
}
.w-30 {
  width: 30%;
}
.w-40 {
  width: 40% !important;
}
.w-50 {
  width: 50%;
}
.w-60 {
  width: 60%;
}
.w-70 {
  width: 70%;
}
.w-100 {
  width: 100%;
}
// 字体大小
.text-22rpx {
  font-size: 22rpx;
}
.text-24rpx {
  font-size: 24rpx;
}
.text-26rpx {
  font-size: 26rpx;
}
.text-30rpx {
  font-size: 30rpx;
}
.text-32rpx {
  font-size: 32rpx;
}
.text-34rpx {
  font-size: 34rpx;
}
.font-36 {
  font-size: 36rpx;
}
.bold {
  font-weight: 600;
}
//相对定位
.relative {
  position: relative;
}

//绝对定位
.absolute {
  position: absolute;
}

//阴影
.shadow {
  box-shadow: 1px 1px 6px 0 rgba(0, 0, 0, 0.2);
}
.shadow1 {
  box-shadow: 1px 1px 6px 0 rgba(0, 0, 0, 0.6);
}
.shadow-card {
  box-shadow: rgba(0, 0, 0, 0.08) 0px 0px 3px 1px;
}

.line-12 {
  line-height: 1.2;
}
.line-15 {
  line-height: 1.5;
}
.line-18 {
  line-height: 1.8;
}
.line-20 {
  line-height: 2;
}
.line-25 {
  line-height: 2.5;
}
.line-30 {
  line-height: 3;
}
.line-40 {
  line-height: 4;
}
.line-50 {
  line-height: 5;
}
.z-index {
  z-index: 1000;
}
.border-r-10 {
  border-radius: 10rpx;
}
.border-r-15 {
  border-radius: 15rpx;
}
.border-r-20 {
  border-radius: 20rpx;
}
.border-b {
  border-bottom: 2rpx solid #d7d6d6;
}
.text-wrap {
  word-wrap: break-word; /* 或使用 overflow-wrap: break-word; */
}
.text-pre-wrap {
  white-space: pre-wrap;
}
.text-white-space {
  white-space: normal;
}
.bg-img {
  box-sizing: border-box;
  width: 100%;
  // background-attachment: fixed;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(125deg, #ffdede 0%, #ebeffa 20%, #ffffff 100%) !important;
  // background-image: url('@/static/img/commonBg.png');
  /* 覆盖整个区域，保持图片比例 */
  // background-repeat: no-repeat;
  // background-position: center;
  // background-size: cover;
}
.bg-img-h {
  width: 100%;
  height: 100%;
  // min-height: 100vh;
  background: linear-gradient(125deg, #ffdede 0%, #ebeffa 20%, #ffffff 100%) !important;
  // background-image: url('@/static/img/commonBg.png');
  /* 覆盖整个区域，保持图片比例 */
  // background-repeat: no-repeat;
  // background-position: center;
  // background-size: cover;
  // background-attachment: fixed;
}
.subText {
  font-size: 24rpx;
  color: #888888;
}
.mainText {
  font-size: 32rpx;
  color: #333333;
}
.color-8 {
  color: #888888;
}
.color-3 {
  color: #333333;
}
// .wd-upload__close {
//   display: none !important;
// }
