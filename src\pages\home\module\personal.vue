<template>
  <view
    class="page-container"
    style="position: relative; width: 100%; height: 100vh"
    @touchstart="onTouchStart"
    @touchend="onTouchEnd"
  >
    <z-paging ref="pagingRef" v-model="pageData" @query="queryList" :paging-style="pageStyle">
      <template #top>
        <CustomNavBar :fixed="false">
          <template #left>
            <scroll-view
              :scroll-x="true"
              :scroll-left="scrollLeft"
              :scroll-into-view="scrollIntoView"
              :scroll-with-animation="false"
              class="scroll-view"
            >
              <view class="scroll-content" :class="{ 'no-animation': !animationEnabled }">
                <view
                  class="content_list_for"
                  v-for="(item, index) in tagList"
                  :key="index"
                  :id="`tab-${index}`"
                  @click="handSelect(index, item)"
                  :class="{
                    'tab-active': selectIndex == index,
                    'tab-inactive': selectIndex != index,
                  }"
                >
                  <view
                    class="text-ellipsis relative tab-text p-r-40rpx"
                    :class="selectIndex == index ? 'title' : ' c-#888 font-weight-500'"
                  >
                    {{ truncateText(item.expectedPositions, 6) }}
                    <view
                      class="tab-indicator"
                      :class="{
                        'indicator-show': selectIndex == index,
                        'indicator-hide': selectIndex != index,
                      }"
                    >
                      <wd-img :width="14" :height="14" :src="tabs" class="indicator-icon" />
                    </view>
                  </view>
                </view>
              </view>
            </scroll-view>
          </template>
          <template #right>
            <image
              class="w-40rpx h-40rpx m-r-40rpx m-t-4rpx"
              src="/static/img/edit.png"
              mode="aspectFill"
              @click="goJobExpectations"
            ></image>
          </template>
          <template #content>
            <view class="page-top">
              <view class="content_search_list content_search-p-t">
                <view class="content_search_list_flex">
                  <view
                    class="content_list_left content_list_left-w"
                    style="display: flex; flex-direction: row"
                  >
                    <view
                      class="content_list_left_for type-tab-item"
                      v-for="(item, index) in typeList"
                      :key="index"
                      @click="handType(index)"
                      :class="{
                        'type-tab-active': selectTag === index,
                        'type-tab-inactive': selectTag !== index,
                      }"
                    >
                      <view class="type-tab-content">
                        <view
                          class="type-tab-text"
                          :class="
                            selectTag === index
                              ? 'content_list_left_color'
                              : 'content_list_left_color1'
                          "
                        >
                          {{ item.name }}
                        </view>
                        <view
                          class="type-tab-indicator"
                          :class="{
                            'indicator-visible': selectTag === index,
                            'indicator-hidden': selectTag !== index,
                          }"
                        ></view>
                      </view>
                    </view>
                    <view class="content_list_adress" @click="goFilter">
                      <view style="white-space: nowrap" class="text-28rpx c-#666">筛选</view>
                      <wd-icon name="caret-down-small" size="18px" color="#999"></wd-icon>
                    </view>
                  </view>

                  <view class="content_list_adress-1 m-t-[-10rpx]" @click="searchPosition">
                    <view class="text-24rpx c-#333">{{ truncateText(cityName, 6) }}</view>

                    <wd-img :width="12" :height="12" :src="location" />
                  </view>
                </view>
              </view>
            </view>
          </template>
        </CustomNavBar>
      </template>
      <!-- 位置筛选区域 -->
      <view class="bg_flex-1" v-if="selectTag === 1">
        <view
          class="bg_box"
          v-for="(item, index) in positionList"
          :key="index"
          :class="activeChange === index ? 'activePositin' : 'nomalPositin'"
          @click="changePosition(index)"
        >
          {{ item.name }}
        </view>
      </view>

      <view class="page_list">
        <view class="page_flex" v-for="(item, index) in pageData" :key="`${selectIndex}-${index}`">
          <view class="page_flex_colom" @click="goDetail(item.id, item.companyId)">
            <view class="page_flex_list">
              <view class="flex-c">
                <wd-img :width="33" :height="16" :src="jz" v-if="item.isRecruit === 1" />
                <view class="page_left">
                  {{ item.positionName }}
                </view>
                <view class="stateType" v-if="item.jobType === 2 || item.jobType === 3">
                  {{ item.jobType === 2 ? '兼职' : item.jobType === 3 ? '实习' : '' }}
                </view>
              </view>

              <view class="page_right salary">
                <text>{{ item.workSalaryBegin }}</text>
                <text v-if="item.workSalaryEnd">-</text>
                <text v-if="item.workSalaryEnd">{{ item.workSalaryEnd }}</text>
              </view>
            </view>
            <view class="page_flex_list">
              <view class="page_left_1">{{ item.name }}·{{ item.sizeName }}</view>
              <view class="page_right_flex">
                <wd-icon name="location" size="14px" color="#999"></wd-icon>
                <view class="page_right_distance">
                  {{ item.distanceMeters ? item.distanceMeters : item.districtName }}
                </view>
              </view>
            </view>
            <view class="bg_flex">
              <view class="bg_box" v-for="(subName, index) in item.positionKey" :key="index">
                {{ subName }}
              </view>
            </view>
            <view class="bg_end">
              <view class="bg_left">
                <image
                  v-if="item.sex === 1"
                  class="bg_left_icon"
                  :src="item.hrPositionUrl ? item.hrPositionUrl : '/static/header/jobhunting1.png'"
                  mode="aspectFill"
                ></image>
                <image
                  v-else
                  class="bg_left_icon"
                  :src="item.hrPositionUrl ? item.hrPositionUrl : '/static/header/jobhunting2.png'"
                  mode="aspectFill"
                ></image>
                <view class="bg_left_flex">
                  <view class="bg_left_name">
                    {{ item.hrPositionName }}
                    <text v-if="item.hrPosition">·</text>
                    {{ item.hrPosition }}
                  </view>
                  <view class="bg_left_date">{{ item.activityStatus }}</view>
                </view>
              </view>
              <view class="flex-c">
                <view class="bg_right m-r-20rpx" @click.stop="goJob(item)">
                  <image
                    class="bg_right_icon-1"
                    src="/static/img/td-job-card.png"
                    mode="aspectFill"
                  ></image>
                </view>
                <view class="bg_right" @click.stop="goChat(item)">
                  <image
                    class="bg_right_icon"
                    src="/static/images/home/<USER>"
                    mode="aspectFill"
                  ></image>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <template #bottom>
        <customTabbar name="home" />
      </template>
    </z-paging>
    <godHorse
      v-if="params.entity.provinceCode"
      @handleSentResumes="handleSentResumes"
      :params="{
        cityCode: params.entity.cityCode,
        districtCode: params.entity.districtCode,
        provinceCode: params.entity.provinceCode,
      }"
    ></godHorse>
    <resumes-sent
      v-model:show="sentResumesBool"
      :params="{
        cityCode: params.entity.cityCode,
        districtCode: params.entity.districtCode,
        positionCode: params.entity.expectedPositions,
        provinceCode: params.entity.provinceCode,
      }"
    />
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBarBig.vue'
import customTabbar from '@/components/common/custom-tabbar.vue'
import { getqueryList, getmyAddress, positionInfoByjob } from '@/interPost/home'
import { numberTokw } from '@/utils/common'
import { useLoginStore, useResumeStore } from '@/store'
import resumesSent from '@/components/common/resumes-sent.vue'
import location from '@/static/img/location.png'
import godHorse from '@/components/god-horse/index.vue'
import tabs from '@/static/img/tabs.png'
import jz from '@/static/img/home/<USER>'
// 文本截断函数 - 超过指定字数显示省略号
const truncateText = (text: string, maxLength: number = 6): string => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}
const { getDictLabel } = useDictionary()
defineOptions({
  name: 'HomePersonal',
})
const message = useMessage()
const { getCurrentLocation } = useLocationPermission()
const { userRoleIsRealName } = useUserInfo()
const { sendGreetingMessage, sendResumeMessage } = useIMConversation()
const { bool: sentResumesBool, setTrue: sentResumesBoolTrue } = useBoolean()
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const tab = ref(0)
const resumeStore = useResumeStore()
// vuex数据
const loginStore = useLoginStore()
const title = ref('')
const activeChange = ref(0)
const cityName = ref('')
const homeCity = ref({})

const params = reactive({
  orderBy: {},
  entity: {
    // 省
    provinceName: '',
    provinceCode: '',
    // 市
    cityName: '',
    cityCode: '',
    // 区
    districtName: '',
    districtCode: '',
    // 关键字
    keyword: '',
    baseInfoId: null,
    expectedCity: '',
    expectedCityCode: '',
    expectedIndustry: '',
    expectedIndustryCode: '',
    expectedPositions: '',
    expectedPositionsCode: '',
    jobType: null,
    salaryExpectationEnd: null,
    salaryExpectationStart: null,
    workEducational: null,
    isNews: null,
    isRecruit: null,
    distanceMeters: null,
    lon: null,
    lat: null, // 尾度
    workSalaryBegin: '',
    workSalaryEnd: '',
    sizeName: '',
    workExperienceStart: '',
    workExperienceEnd: '',
  },
})
const selectTag = ref(0)
const selectIndex = ref(0)
// 控制动画是否启用，避免初始化时的抖动
const animationEnabled = ref(false)
const typeList = ref([
  {
    name: '推荐',
    value: '1',
  },
  {
    name: '附近',
    value: '2',
  },
  {
    name: '最新',
    value: '3',
  },
  {
    name: '急招',
    value: '4',
  },
])
// 求职期望
const goJobExpectations = () => {
  uni.navigateTo({
    url: '/resumeRelated/jobExpectations/index?isShow=' + 'home',
  })
}
const userToRealName = () => {
  if (!userRoleIsRealName.value) {
    message
      .confirm({
        title: '提示',
        msg: '请先实名认证',
      })
      .then(() => {
        uni.navigateTo({
          url: '/setting/identityAuth/index',
        })
      })
      .catch()
    return Promise.reject(new Error('请先实名认证'))
  }
  return Promise.resolve(1)
}
// 去沟通item
const goChat = async (item: AnyObject) => {
  try {
    await userToRealName()
    const hxUserInfoVO = item?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      sendGreetingMessage(hxUserInfoVO.username, item)
    }
  } catch (error) {}
}
const tagList = ref([])
async function handleSentResumes() {
  try {
    await userToRealName()
    sentResumesBoolTrue()
  } catch (error) {}
}
const goJob = async (item: AnyObject) => {
  try {
    await userToRealName()
    const hxUserInfoVO = item?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      const num = await sendResumeMessage(hxUserInfoVO.username, item)
      if (!num) {
        message
          .confirm({
            title: '提示',
            msg: '请完善简历后再投递',
          })
          .then(() => {
            uni.navigateTo({
              url: '/resumeRelated/AttachmentResume/index',
            })
          })
          .catch()
      }
    }
  } catch (error) {}
}
const goFilter = () => {
  uni.navigateTo({
    url: '/resumeRelated/filter/index',
  })
}
const goDetail = (id: any, companyId: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${id}&companyId=${companyId}`,
  })
}
// 地址搜索
const searchPosition = () => {
  uni.navigateTo({
    url: '/resumeRelated/HomeRegion/index',
  })
}
// 附近
const positionList = ref([
  {
    name: '不限',
  },
  {
    name: '5km',
  },
  {
    name: '10km',
  },
  {
    name: '20km',
  },
  {
    name: '30km',
  },
])
// 获取3个职位
const jobList = async () => {
  const res: any = await positionInfoByjob()
  console.log('获取3个职位=========================')
  if (res.code === 0) {
    tagList.value = res.data
    params.entity.expectedPositions = tagList.value[0]?.expectedPositions
    title.value = tagList.value[0].expectedPositions
    await getRegenList()
  }
}
// 附近距离切换
const changePosition = (index) => {
  activeChange.value = index
  params.entity.distanceMeters = null
  if (index === 0) {
    params.entity.distanceMeters = null
  }
  if (index === 1) {
    params.entity.distanceMeters = 5 * 1000
  }
  if (index === 2) {
    params.entity.distanceMeters = 10 * 1000
  }
  if (index === 3) {
    params.entity.distanceMeters = 20 * 1000
  }
  if (index === 4) {
    params.entity.distanceMeters = 30 * 1000
  }
  pagingRef.value.reload()
}
// 切换列表
const handType = (index: any) => {
  selectTag.value = index
  params.entity.isNews = null
  params.entity.distanceMeters = null
  params.entity.isRecruit = null
  if (selectTag.value === 1) {
    getmyAddressList()
  }
  if (selectTag.value === 2) {
    params.entity.isNews = 1
  }
  if (selectTag.value === 3) {
    params.entity.isRecruit = 1
  }
  pagingRef.value.reload()
}
// 搜索
const confirm = () => {
  pagingRef.value.reload()
}
// 获取地址
const getmyAddressList = async () => {
  const res: any = await getmyAddress()
  if (res.code === 0) {
    if (res.data?.lat) {
      params.entity.lat = res.data.lat
      params.entity.lon = res.data.lon
    } else {
      uni.navigateTo({
        url: '/setting/AdressMange/index',
      })
    }
  }
}
// 获取列表positionKey
const queryList = async (page, size) => {
  console.log('获取列表position')
  params.entity.expectedPositions = tagList.value[selectIndex.value].expectedPositions
  title.value = tagList.value[selectIndex.value].expectedPositions
  pageSetInfo(page, size)
  const res: any = await getqueryList({
    ...params,
    size: pageInfo.pageSize,
    page: pageInfo.pageNum,
  })
  if (res.code === 0) {
    res.data?.list &&
      res.data.list.forEach(async (ele: any) => {
        ele.sizeName = await getDictLabel(100, ele.sizeName)
        ele.positionKey = ele.positionKey && ele.positionKey.split(',')
        ele.workSalaryBegin =
          ele.workSalaryBegin === 0 ? '面议' : numberTokw(ele.workSalaryBegin + '')
        ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '' : numberTokw(ele.workSalaryEnd + '')
        ele.distanceMeters = ele.distanceMeters
          ? Math.floor(parseInt(ele.distanceMeters) / 1000) + 'km'
          : ''
      })

    pagingRef.value.complete(res.data.list)
  }
}
// 获取查询地址和筛选条件
const getRegenList = () => {
  // 设置查询条件
  const filter = resumeStore?.fillterObg
  if (filter) {
    params.entity.workSalaryBegin = filter.workSalaryBegin
    params.entity.workSalaryEnd = filter.workSalaryEnd
    params.entity.sizeName = filter.sizeName
    params.entity.workEducational = filter.workEducational
    params.entity.workExperienceStart = filter.workExperienceStart
    params.entity.workExperienceEnd = filter.workExperienceEnd
  }

  // 获取城市选中

  const activeIndex = loginStore.homeJobAvtive
  console.log(activeIndex, 'activeIndex==')
  const cityKey = `homeCity${activeIndex + 1}`
  const setCityFn = `sethomeCity${activeIndex + 1}`
  const defaultCity = tagList.value[activeIndex]
  console.log(tagList.value[activeIndex], 'tagList.value[activeIndex]')

  // 获取或设置城市信息

  const cityData = loginStore[cityKey]?.provinceName ? loginStore[cityKey] : defaultCity

  loginStore[setCityFn]({
    provinceName: cityData.provinceName,
    provinceCode: Number(cityData.provinceCode),
    cityCode: Number(cityData.cityCode),
    cityName: cityData.cityName,
    districtCode: cityData.provinceCode ? Number(cityData.districtCode) : '',
    districtName: cityData.districtName || '',
  })

  // 设置参数
  params.entity.provinceName = cityData.provinceName
  params.entity.provinceCode = cityData.provinceCode
  params.entity.cityName = cityData.cityName
  params.entity.cityCode = cityData.cityCode
  params.entity.districtName = cityData.districtName || ''
  params.entity.districtCode = cityData.districtCode || ''
  // 设置显示的城市名
  cityName.value = cityData.districtName
    ? cityData.districtName
    : cityData.cityName || cityData.cityName

  pagingRef.value.reload()
}

// 简单的滑动手势支持
let touchStartX = 0
let touchStartY = 0

const onTouchStart = (e: any) => {
  if (e.touches && e.touches[0]) {
    touchStartX = e.touches[0].clientX
    touchStartY = e.touches[0].clientY
  }
}

const onTouchEnd = (e: any) => {
  if (!e.changedTouches || !e.changedTouches[0]) return

  const touchEndX = e.changedTouches[0].clientX
  const touchEndY = e.changedTouches[0].clientY
  const deltaX = touchEndX - touchStartX
  const deltaY = Math.abs(touchEndY - touchStartY)

  // 判断是否为有效的水平滑动
  const minSwipeDistance = 50
  const isHorizontalSwipe = Math.abs(deltaX) > deltaY && Math.abs(deltaX) > minSwipeDistance

  if (isHorizontalSwipe) {
    if (deltaX > 0 && selectIndex.value > 0) {
      // 向右滑动，切换到上一个tab
      const newIndex = selectIndex.value - 1
      const targetItem = tagList.value[newIndex]
      if (targetItem) {
        handSelect(newIndex, targetItem)
        uni.vibrateShort({ type: 'light' })
      }
    } else if (deltaX < 0 && selectIndex.value < tagList.value.length - 1) {
      // 向左滑动，切换到下一个tab
      const newIndex = selectIndex.value + 1
      const targetItem = tagList.value[newIndex]
      if (targetItem) {
        handSelect(newIndex, targetItem)
        uni.vibrateShort({ type: 'light' })
      }
    }
  }
}

// 滚动到指定tab位置，确保选中项完全可见
const scrollLeft = ref(0)
const scrollIntoView = ref('')
const scrollToTab = (index: number) => {
  nextTick(() => {
    // 使用scroll-into-view确保选中项完全可见
    scrollIntoView.value = `tab-${index}`
    // 清空scroll-into-view，避免影响后续操作
    setTimeout(() => {
      scrollIntoView.value = ''
    }, 100) // 缩短延时，提高响应速度
  })
}

const handSelect = (index: any, item: any) => {
  console.log('handSelect called:', index, item.expectedPositions)

  selectIndex.value = index
  loginStore.sethomeJobAvtive(index)
  title.value = item.expectedPositions

  // 设置期望职位参数
  params.entity.expectedPositions = item.expectedPositions

  // 滚动到对应的tab位置
  scrollToTab(index)

  // 重新获取数据
  getRegenList()
  pagingRef.value.reload()
}
onShow(async () => {
  if (resumeStore.isRefresh === 1) {
    await jobList()
    resumeStore.setIsRefresh(0)
  }
})
onMounted(async () => {
  await uni.$onLaunched
  try {
    await getCurrentLocation()
  } catch (error) {}
  loginStore.sethomeJobAvtive(selectIndex.value)
  await nextTick()
  await jobList()

  // 初始化时设置滚动位置
  setTimeout(() => {
    scrollToTab(selectIndex.value)
  }, 100)
})
uni.$on('refresh-a-page', getRegenList)
uni.$on('refresh-a-jobList', jobList)
onUnmounted(() => {
  uni.$off('refresh-a-page', getRegenList)
  uni.$off('refresh-a-jobList', jobList)
})
</script>

<style lang="scss" scoped>
:deep(.wd-tabs) {
  background: transparent;
  .wd-tabs__nav {
    background: transparent;
  }
  .wd-tabs__nav-item {
    &.is-active {
      flex: none;
      margin-right: 40rpx;
      font-size: 48rpx;
    }
  }
}
/* 标签文字过渡 - 移除动画避免抖动 */
.tab-text {
  position: relative;
  font-size: 36rpx !important;
  transition:
    font-size 0.2s ease,
    color 0.2s ease; /* 只对字体大小和颜色做过渡 */
  transform-origin: center;
  backface-visibility: hidden; /* 防止闪烁 */
  /* 选中状态 */
  &.title {
    font-size: 48rpx !important;
    font-weight: 500;
    color: #000000;
  }
}
.shtop {
  padding-top: -20rpx;
}
.salary {
  color: #ff8080 !important;
}
.custom-class {
  background: transparent;
}
.content_list_left-w {
  width: calc(100% - 200rpx);
}
.content_search-p {
  padding: 30rpx 40rpx;
}
::v-deep .uni-scroll-view-content {
  text-align: left !important;
}
.stateType {
  padding: 0rpx 10rpx;
  margin-left: 10rpx;
  font-size: 20rpx;
  color: #888888;
  text-align: center;
  border: 1rpx solid #888888;

  border-radius: 6rpx;
}
.sx {
  background: #adbaff;
}
.jz {
  background: #fda283;
}
/* 水平滚动容器样式 */
.scroll-view {
  width: 100%;
  /* 确保滚动容器有足够的内边距，避免选中项被遮挡 */
  // padding: 0 20rpx;
  white-space: nowrap;
}

.scroll-content {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  white-space: nowrap;
}

.content_list_for {
  display: inline-block;
  flex-shrink: 0; /* 防止元素被压缩 */
  margin-right: 20rpx; /* 添加右边距 */
  white-space: nowrap;
  vertical-align: top;
  transform: translateZ(0); /* 启用硬件加速 */
  backface-visibility: hidden; /* 防止闪烁 */
  /* 移除缩放动画，避免抖动和隐藏问题 */
  &.tab-active {
    transform: translateZ(0); /* 移除缩放效果 */

    .tab-text {
      transition:
        font-size 0.2s ease,
        color 0.2s ease; /* 只对字体和颜色做过渡 */
    }
  }

  &.tab-inactive {
    transform: translateZ(0);

    .tab-text {
      transition:
        font-size 0.2s ease,
        color 0.2s ease; /* 只对字体和颜色做过渡 */
    }
  }

  .content_list_border_1 {
    padding-right: 30rpx;
    padding-left: 30rpx;
  }
}
/* 标签指示器动画 - 优化抖动 */
.tab-indicator {
  position: absolute;
  right: 20rpx;
  bottom: -14rpx;
  transition: all 0.2s ease-out; /* 缩短动画时长 */
  will-change: opacity, transform; /* 优化渲染性能 */
  backface-visibility: hidden; /* 防止闪烁 */

  &.indicator-show {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  &.indicator-hide {
    opacity: 0;
    transform: translateY(5rpx) scale(0.9); /* 减少位移和缩放幅度 */
  }

  .indicator-icon {
    transition: all 0.2s ease-out; /* 统一动画时长 */
  }
}
/* 移除点击反馈动画，避免抖动 */
/* 页面初始化时禁用动画，避免从tabbar切换时的抖动 */
.scroll-view {
  /* 确保滚动容器稳定 */
  -webkit-overflow-scrolling: touch;
}

.scroll-content {
  /* 防止初始化时的布局抖动 */
  min-height: 60rpx;
  /* 页面刚加载时禁用动画 */
  &.no-animation {
    * {
      transition: none !important;
      animation: none !important;
    }
  }
}
/* 移除复杂的关键帧动画，使用简单的transition避免抖动 */
.content_list_for {
  /* 移除可能导致抖动的关键帧动画，改用简单的transition */
  &.tab-active {
    .tab-text {
      /* 使用transition而不是animation，避免抖动 */
      transition: all 0.25s ease-out;
    }
  }

  &.tab-inactive {
    .tab-text {
      /* 使用transition而不是animation，避免抖动 */
      transition: all 0.25s ease-out;
    }
  }
}
.job-tag {
  padding: 0 20rpx;
  margin-right: 10rpx;
  font-size: 24rpx;
  color: #fff;
  background: #ff5151;
  border-radius: 20rpx 0rpx 20rpx 0rpx;
}
.content_search-p-t {
  padding: 0rpx 40rpx 0rpx;
}

.page_left_1 {
  font-size: 24rpx !important;
  font-weight: 400;
  line-height: 44rpx;
  color: #666;
}

.bg_end {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-top: 10rpx;
  .bg_right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 132rpx;
    height: 64rpx;
    text-align: center;
    background: #fff4f4;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    &_icon {
      width: 50rpx;
      height: 50rpx;
    }
    &_icon-1 {
      width: 50rpx;
      height: 50rpx;
    }
  }

  .bg_left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .bg_left_icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50rpx;
    }

    .bg_left_flex {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-left: 15rpx;

      .bg_left_name {
        font-size: 24rpx;
        font-weight: 400;
        line-height: 44rpx;
        color: #555555;
      }

      .bg_left_date {
        font-size: 22rpx;
        font-weight: 400;
        color: #666;
        // line-height: 44rpx;
      }
    }
  }
}

.bg_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 100%;
  padding-top: 0rpx !important;
  padding-bottom: 14rpx;

  .bg_box {
    padding: 0rpx 10rpx;
    // margin: 14rpx 0;
    margin-top: 14rpx;
    margin-right: 24rpx;
    font-size: 22rpx;
    font-weight: 400;
    line-height: 44rpx;
    color: #888888;
    background: #f3f3f3;
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}
.bg_flex-1 {
  z-index: 1000;
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  padding: 0rpx 40rpx 0rpx;
  padding-bottom: 20rpx;
  margin-top: 0rpx;
  .activePositin {
    color: #4d8fff;
    border: 1rpx solid #4d8fff;
  }
  .nomalPositin {
    color: #000;
  }
  .bg_box {
    padding: 0rpx 20rpx;
    // margin: 14rpx 0;
    margin-right: 22rpx;
    font-size: 24rpx;
    font-weight: 400;
    line-height: 44rpx;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}

.page_list {
  box-sizing: border-box;
  width: 100%;
  padding: 0 40rpx;
  // margin-bottom: 200rpx;

  .page_flex {
    width: 100%;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    .page_flex_colom {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: 100%;

      .page_flex_list {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .page_right_flex {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 5rpx 0;

          .page_left_1 {
            font-size: 24rpx !important;
            font-weight: 400;
            line-height: 44rpx;
            color: #666;
          }

          .page_right_distance {
            font-size: 22rpx;
            font-weight: 400;
            line-height: 44rpx;
            color: #888888;
          }
        }

        .page_left {
          font-size: 28rpx;
          font-weight: 600;
          line-height: 44rpx;
          color: #333333;
        }

        .page_right {
          font-size: 28rpx;
          font-weight: 600;
          line-height: 44rpx;
          color: #888888;
        }
      }
    }
  }
}
.content_list_adress {
  display: flex;
  flex-direction: row;
  align-items: flex-center;
  font-size: 24rpx;
  font-weight: 400;
  color: #555555;
}
.content_list_adress-1 {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.content_search_list_flex {
  display: flex !important;
  flex-direction: row;
  align-items: center;
  justify-content: space-between !important;
  padding-bottom: 30rpx;
}

.navigation-bar-betwween {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 40rpx;
}

.back-button {
  position: absolute;
  left: 10px;
}

.title {
  font-size: 48rpx;
  font-weight: 500;
  color: #000000;
}
/* 文本省略样式 - 超过6个字显示省略号 */
.text-ellipsis {
  display: inline-block;
  max-width: 12em; /* 6个字的宽度，1个中文字符约等于2em */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.content_list_right-1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 84rpx;
  height: 84rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
  ._img {
    width: 44rpx;
    height: 44rpx;
  }
}
.content_list_right {
  display: flex;
  flex-direction: row;
  align-items: center;

  .content_list_icon {
    display: flex;
    align-items: center;
    padding: 16rpx 10rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    ._img {
      width: 50rpx;
      height: 50rpx;
    }
  }
}
/* 类型标签容器样式 */
.content_list_left_for {
  display: flex;
  flex-direction: row;
  padding-right: 30rpx;
  border-bottom: 6rpx solid transparent;
}
/* 新增的类型标签样式 - 简化过渡效果，避免抖动 */
.type-tab-item {
  position: relative;
  /* 移除复杂的动画，只保留简单过渡 */
  transition: none;
  transform: translateZ(0); /* 启用硬件加速 */

  &.type-tab-active {
    /* 移除缩放效果，避免抖动 */
    transform: translateZ(0);
  }

  &.type-tab-inactive {
    transform: translateZ(0);
  }
}

.type-tab-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.type-tab-text {
  font-size: 26rpx;
  /* 简化过渡效果，只对颜色和字体大小做过渡 */
  transition:
    color 0.2s ease,
    font-size 0.2s ease;
  transform-origin: center;
}

.type-tab-indicator {
  position: absolute;
  bottom: -6rpx;
  left: 50%;
  width: 58rpx;
  height: 6rpx;
  background: #ff9191;
  border-radius: 3rpx;
  /* 简化过渡效果，只对透明度做过渡 */
  transition: opacity 0.2s ease;
  transform: translateX(-50%);
  transform-origin: center;

  &.indicator-visible {
    opacity: 1;
  }

  &.indicator-hidden {
    opacity: 0;
  }
}

.content_list_left_color {
  font-size: 28rpx !important;
  font-weight: 600;
  color: #000000;
  /* 移除缩放效果，避免抖动 */
}

.content_list_left_color1 {
  font-size: 28rpx !important;
  font-weight: 400;
  color: #666666;
  /* 移除缩放效果，避免抖动 */
}
/* 移除点击反馈动画和关键帧动画，避免抖动 */

.content_list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  .content_list_left {
    position: relative;
    // width: 80%;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: calc(100% - 100rpx);
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    ._img {
      position: absolute;
      right: 10rpx;
      width: 30rpx;
      height: 30rpx;
    }
    .content_list_left_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: left;
      width: 100%;
      padding: 10rpx 60rpx 10rpx 20rpx;
      white-space: nowrap;
      // background: red;
    }
  }
}

.select_border {
  width: 100%;
  padding: 11rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 44rpx;
  color: #333333;
  text-align: center;
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
}

.select_noBorder {
  font-size: 24rpx;
  line-height: 44rpx;
  color: #3e3e56;
  text-align: center;
}

.content_list_left_xian {
  width: 58rpx;
  height: 8rpx;
  font-weight: bold;
  background: #ff9191;
  border-radius: 2rpx 2rpx 2rpx 2rpx;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0rpx 40rpx 0rpx;
  padding-bottom: 0;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 20rpx 30rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;
        ._image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.content {
  // height: 100%;
  // background-color: rgba(244, 244, 244, 1);
  width: 100%;
}

.start_ICON {
  ._icon {
    width: 56rpx;
    height: 56rpx;
  }
}
/* 滑动指示器样式 */
.swipe-indicator {
  position: fixed;
  top: 50%;
  z-index: 9999;
  pointer-events: none;
  transition: opacity 0.2s ease;
  transform: translateY(-50%);

  &.swipe-next {
    right: 40rpx;
  }

  &.swipe-prev {
    left: 40rpx;
  }

  .swipe-indicator-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10rpx);
    border-radius: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

    .swipe-indicator-text {
      margin-top: 8rpx;
      font-size: 24rpx;
      font-weight: 500;
      color: #4d8fff;
    }
  }
}
/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-fade-leave-active {
  transition: all 0.2s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
/* 原生Swiper样式 */
.swiper-container {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
}
/* 优化页面列表在swiper中的显示 */
.swiper-slide .page_list {
  box-sizing: border-box;
  width: 100%;
  padding: 0 40rpx;
}
/* 占位内容样式 */
.placeholder-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;

  .placeholder-text {
    font-size: 32rpx;
    color: #999;
    text-align: center;
  }
}
</style>
