<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="公司名称">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
        <template #right>
          <view class="submit" @click="submit">确认</view>
        </template>
      </CustomNavBar>
    </template>
    <view class="corporateName">
      <wd-input no-border v-model="company" placeholder="请输入公司名称" />
    </view>
    <wd-message-box />
  </z-paging>
</template>

<script lang="ts" setup>
import { useResumeStore } from '@/store'
import { useMessage } from 'wot-design-uni'
// z-paing===
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const resumeStore = useResumeStore()
// 公司
const company = ref('')
// 初始化
const initNane = ref('')
const message = useMessage()
// 提交
const submit = () => {
  resumeStore.setCompany(company.value)
  uni.navigateBack()
}
// 赋值
onShow(() => {
  company.value = resumeStore.company
})
onLoad(async (options) => {
  await nextTick()
  company.value = options.company
  initNane.value = options.company
})
// 返回
const back = () => {
  if (company.value !== initNane.value) {
    message
      .confirm({
        title: '提示',
        msg: '您有内容未提交保存,确认返回吗?',
      })
      .then(() => {
        resumeStore.setCompany(initNane.value)
        uni.navigateBack()
      })
  } else {
    uni.navigateBack()
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  text-align: left;
  background-color: transparent;
}
::v-deep .wd-input__placeholder {
  font-size: 40rpx !important;
}
::v-deep .wd-input__inner {
  font-size: 40rpx !important;
  font-weight: 500;
}
.corporateName {
  padding-bottom: 40rpx;
  margin: 40rpx 40rpx;
  border-bottom: 1rpx solid #c0bfbf;
}
</style>
